# Contributing

- [📜 Contributor License Agreement](#-contributor-license-agreement)
- [🤝 Contributor Code of Conduct](#-contributor-code-of-conduct)
- [🪲 Bug Reports, Issues and Questions](#-bug-reports-issues-and-questions)
- [💡 Feature Requests](#-feature-requests)
- [🖌️ Coding Style](#️-coding-style)
- [📁 Documentation](#-documentation)

## 📜 Contributor License Agreement

By contributing, you agree to the Licenses of this repository:

- OpenEMS Edge und Backend
  
    [Eclipse Public License version 2.0](../LICENSE-EPL-2.0)

- OpenEMS UI

    [GNU Affero General Public License version 3](../LICENSE-AGPL-3.0)

## 🤝 Contributor Code of Conduct

By contributing, you agree to respect the [Code of Conduct](CODE_OF_CONDUCT.md) of this repository.

## 🪲 Bug Reports, Issues and Questions

A great way to contribute to the project is to send a detailed report when you encounter an issue. We always appreciate a well-written, thorough bug report, and will thank you for it!

To maintain clear and organized communication, all discussions should take place in the [OpenEMS Community forum](https://community.openems.io/). This helps keep everything together and ensures that conversations are streamlined and accessible to all contributors.

⚠️ *Please refrain from opening empty pull requests solely for the purpose of discussing ideas. Instead, utilize the forum to share and refine your concepts before submitting any code changes.*

## 💡 Feature Requests

Similar to issue reports, feature requests should be submitted to the [OpenEMS Community forum](https://community.openems.io/) for discussion. This helps ensure that all ideas are properly reviewed and discussed by the community.

When submitting a feature request, please be clear about the intended outcome and how it would relate to existing features. Providing detailed information helps in evaluating the request more effectively.

⚠️ *Additionally, avoid submitting duplicate feature requests. Before posting, search for existing requests, and if you find a similar or identical one, please join that discussion instead of creating a new thread.*

## 🖌️ Coding Style

We welcome pull-requests! While we will consider all submissions, we cannot promise that every request will be accepted.

To increase your changes of a merged pull-requests and help us review your code, please follow our [coding guidelines](https://openems.github.io/openems.io/openems/latest/contribute/coding-guidelines.html).

## 📁 Documentation

The documentation site for OpenEMS is hosted on [https://openems.github.io/openems.io/openems/latest](https://openems.github.io/openems.io/openems/latest). We greatly appreciate contributions that improve the quality and clarity of our documentation.

If you would like to contribute by updating or adding a page, please refer to our  [Contribute/Documentation](https://openems.github.io/openems.io/openems/latest/contribute/documentation.html) guide for detailed instructions on how to contribute effectively to the OpenEMS documentation.
