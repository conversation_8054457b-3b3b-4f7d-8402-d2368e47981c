version: 2
updates:
  - package-ecosystem: maven
    directory: "/cnf"
    schedule:
      interval: weekly
    open-pull-requests-limit: 10
    target-branch: develop
    groups:
      influxdb:
        patterns:
          - "com.influxdb:*"
      fastexcel:
        patterns:
          - "org.dhatim:fastexcel"
          - "org.dhatim:fastexcel-reader"
      bouncycastle:
        patterns:
          - "org.bouncycastle:*"
      pax-logging:
        patterns:
          - "org.ops4j.pax.logging:*"
      guava:
        patterns:
          - "com.google.guava:*"
      retrofit2:
        patterns:
          - "com.squareup.retrofit2:*"
      prometheus:
        patterns:
          - "io.prometheus:*"

  - package-ecosystem: npm
    directory: "/ui"
    schedule:
      interval: weekly
    open-pull-requests-limit: 10
    target-branch: develop
    groups:
      openems-ui:
        patterns:
          - "@angular/*"
          - "@angular-devkit/*"
          - "@angular-eslint/*"
          - "@schematics/angular"
          - "@capacitor/*"
          - "@capacitor-community/*"
          - "capacitor-*"
          - "@ionic-native/*"
          - "@ngx-formly/*"
          - "karma-*"
          - "karma"
          - "@stylistic/eslint-plugin"
          - "@typescript-eslint/*"
          - "eslint-*"
          - "eslint"

  - package-ecosystem: "github-actions"
    directory: "/.github/workflows"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    target-branch: develop
