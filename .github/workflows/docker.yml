name: Build OpenEMS Docker Image

on:
  workflow_dispatch:
  push:
    branches:
      - develop
      - main
    tags:
      - "*.*.*"
    
jobs:
  docker:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
      matrix:
        image: [edge, backend, ui-edge, ui-backend]
        include:
          - image: edge
            dockerfile: tools/docker/edge/Dockerfile
            args: ""
          - image: ui-edge
            dockerfile: tools/docker/ui/Dockerfile
            args: "VERSION=openems,openems-edge-docker"
          - image: backend
            dockerfile: tools/docker/backend/Dockerfile
            args: ""
          - image: ui-backend
            dockerfile: tools/docker/ui/Dockerfile
            args: "VERSION=openems,openems-backend-docker"
    permissions:
        packages: write
        contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Get cleaned branch name
        id: clean_name
        run: |
          REF_NAME=$(echo "${{ github.ref }}" | sed -e 's/refs\/heads\///' -e 's/refs\/tags\///' -e 's/release\/v//')
          echo "branch=${REF_NAME}-test" >> "$GITHUB_OUTPUT"

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.REPOSITORY_OWNER }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            openems/${{ matrix.image }}
            ghcr.io/openems/${{ matrix.image }}
          labels: |
            org.opencontainers.image.authors=OpenEMS
            org.opencontainers.image.title=OpenEMS ${{ matrix.image }}
            org.opencontainers.image.vendor=OpenEMS <openems.io>
          tags: |
            type=semver,pattern={{raw}},enable=${{ startsWith(github.ref, 'refs/tags/') }}
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
            type=raw,value=develop,enable=${{ github.ref == 'refs/heads/develop' }}

      - name: Build and push Docker images
        uses: docker/build-push-action@v6
        with:
          file: ${{ matrix.dockerfile }}
          platforms: linux/amd64, linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: ${{ matrix.args }}          