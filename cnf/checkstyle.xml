<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN" "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
    This configuration file was written by the eclipse-cs plugin configuration editor
-->
<!--
    Checkstyle-Configuration: OpenEMS Checkstyle
    Description: none
-->
<module name="Checker">
  <property name="severity" value="warning"/>
  <property name="charset" value="UTF-8"/>
  <property name="fileExtensions" value="java, properties, xml"/>
  <module name="TreeWalker">
    <module name="RequireThis">
      <property name="validateOnlyOverlapping" value="false"/>
    </module>
    <module name="OuterTypeFilename"/>
    <module name="IllegalTokenText">
      <property name="format" value="\\u00(09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
      <property name="message" value="Consider using special escape sequence instead of octal value or Unicode escaped value."/>
      <property name="tokens" value="STRING_LITERAL, CHAR_LITERAL"/>
    </module>
    <module name="AvoidEscapedUnicodeCharacters">
      <property name="allowByTailComment" value="true"/>
      <property name="allowEscapesForControlCharacters" value="true"/>
      <property name="allowNonPrintableEscapes" value="true"/>
    </module>
    <module name="AvoidStarImport"/>
    <module name="OneTopLevelClass"/>
    <module name="NoLineWrap"/>
    <module name="EmptyBlock">
      <property name="option" value="TEXT"/>
      <property name="tokens" value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
    </module>
    <module name="NeedBraces"/>
    <module name="LeftCurly"/>
    <module name="RightCurly">
      <property name="id" value="RightCurlySame"/>
      <property name="tokens" value="LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE,                     LITERAL_DO"/>
    </module>
    <module name="RightCurly">
      <property name="id" value="RightCurlyAlone"/>
      <property name="option" value="alone"/>
      <property name="tokens" value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, STATIC_INIT,                     INSTANCE_INIT"/>
    </module>
    <module name="WhitespaceAround">
      <property name="allowEmptyConstructors" value="true"/>
      <property name="allowEmptyLoops" value="true"/>
      <property name="allowEmptyMethods" value="true"/>
      <property name="allowEmptyTypes" value="true"/>
      <property name="tokens" value="ASSIGN,BAND,BAND_ASSIGN,BOR,BOR_ASSIGN,BSR,BSR_ASSIGN,BXOR,BXOR_ASSIGN,COLON,DIV,DIV_ASSIGN,DO_WHILE,EQUAL,GE,GT,LAMBDA,LAND,LCURLY,LE,LITERAL_CATCH,LITERAL_DO,LITERAL_ELSE,LITERAL_FINALLY,LITERAL_FOR,LITERAL_IF,LITERAL_RETURN,LITERAL_SYNCHRONIZED,LITERAL_TRY,LITERAL_WHILE,LOR,LT,MINUS,MINUS_ASSIGN,MOD,MOD_ASSIGN,NOT_EQUAL,PLUS,PLUS_ASSIGN,QUESTION,RCURLY,SL,SLIST,SL_ASSIGN,SR,SR_ASSIGN,STAR,STAR_ASSIGN,LITERAL_ASSERT,TYPE_EXTENSION_AND"/>
      <message key="ws.notPreceded" value="WhitespaceAround: ''{0}'' is not preceded with whitespace."/>
      <message key="ws.notFollowed" value="WhitespaceAround: ''{0}'' is not followed by whitespace. Empty blocks may only be represented as '{}' when not part of a multi-block statement (4.1.3)"/>
    </module>
    <module name="OneStatementPerLine"/>
    <module name="MultipleVariableDeclarations">
      <property name="severity" value="ignore"/>
      <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
    </module>
    <module name="ArrayTypeStyle"/>
    <module name="MissingSwitchDefault">
      <property name="severity" value="ignore"/>
      <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
    </module>
    <module name="FallThrough"/>
    <module name="UpperEll"/>
    <module name="ModifierOrder"/>
    <module name="EmptyLineSeparator">
      <property name="allowNoEmptyLineBetweenFields" value="true"/>
    </module>
    <module name="SeparatorWrap">
      <property name="id" value="SeparatorWrapDot"/>
      <property name="option" value="nl"/>
      <property name="tokens" value="DOT"/>
    </module>
    <module name="SeparatorWrap">
      <property name="id" value="SeparatorWrapComma"/>
      <property name="option" value="EOL"/>
      <property name="tokens" value="COMMA"/>
    </module>
    <module name="SeparatorWrap">
      <property name="id" value="SeparatorWrapEllipsis"/>
      <property name="option" value="EOL"/>
      <property name="tokens" value="ELLIPSIS"/>
    </module>
    <module name="SeparatorWrap">
      <property name="id" value="SeparatorWrapArrayDeclarator"/>
      <property name="option" value="EOL"/>
      <property name="tokens" value="ARRAY_DECLARATOR"/>
    </module>
    <module name="SeparatorWrap">
      <property name="id" value="SeparatorWrapMethodRef"/>
      <property name="option" value="nl"/>
      <property name="tokens" value="METHOD_REF"/>
    </module>
    <module name="PackageName">
      <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
      <message key="name.invalidPattern" value="Package name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="TypeName">
      <message key="name.invalidPattern" value="Type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="MemberName">
      <property name="format" value="^[a-z_][a-zA-Z0-9]*$"/>
      <message key="name.invalidPattern" value="Member name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="ParameterName">
      <property name="accessModifiers" value="public"/>
      <property name="format" value="^[a-z]([a-zA-Z0-9]*)?$"/>
      <message key="name.invalidPattern" value="Parameter name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="LambdaParameterName">
      <property name="format" value="^[a-z][a-zA-Z0-9]*?$"/>
      <message key="name.invalidPattern" value="Lambda parameter name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="CatchParameterName">
      <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
      <message key="name.invalidPattern" value="Catch parameter name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="LocalVariableName">
      <property name="format" value="^[a-z]([a-zA-Z0-9]*)?$"/>
      <message key="name.invalidPattern" value="Local variable name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="ClassTypeParameterName">
      <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]$)"/>
      <message key="name.invalidPattern" value="Class type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="MethodTypeParameterName">
      <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]$)"/>
      <message key="name.invalidPattern" value="Method type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="InterfaceTypeParameterName">
      <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]$)"/>
      <message key="name.invalidPattern" value="Interface type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="NoFinalizer"/>
    <module name="GenericWhitespace">
      <message key="ws.notPreceded" value="GenericWhitespace ''{0}'' is not preceded with whitespace."/>
      <message key="ws.followed" value="GenericWhitespace ''{0}'' is followed by whitespace."/>
      <message key="ws.preceded" value="GenericWhitespace ''{0}'' is preceded with whitespace."/>
      <message key="ws.illegalFollow" value="GenericWhitespace ''{0}'' should followed by whitespace."/>
    </module>
    <module name="Indentation">
      <property name="severity" value="ignore"/>
      <property name="arrayInitIndent" value="2"/>
      <property name="basicOffset" value="2"/>
      <property name="caseIndent" value="2"/>
      <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
    </module>
    <module name="AbbreviationAsWordInName">
      <property name="allowedAbbreviationLength" value="1"/>
      <property name="ignoreFinal" value="false"/>
    </module>
    <module name="OverloadMethodsDeclarationOrder"/>
    <module name="VariableDeclarationUsageDistance"/>
    <module name="MethodParamPad"/>
    <module name="NoWhitespaceBefore">
      <property name="allowLineBreaks" value="true"/>
      <property name="tokens" value="COMMA, SEMI, POST_INC, POST_DEC, DOT, ELLIPSIS, METHOD_REF"/>
    </module>
    <module name="ParenPad"/>
    <module name="OperatorWrap">
      <property name="option" value="NL"/>
      <property name="tokens" value="BAND, BOR, BSR, BXOR, DIV, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR,                     LT, MINUS, MOD, NOT_EQUAL, PLUS, QUESTION, SL, SR, STAR, METHOD_REF "/>
    </module>
    <module name="AnnotationLocation">
      <property name="id" value="AnnotationLocationMostCases"/>
      <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF"/>
    </module>
    <module name="AnnotationLocation">
      <property name="id" value="AnnotationLocationVariables"/>
      <property name="allowSamelineMultipleAnnotations" value="true"/>
      <property name="tokens" value="VARIABLE_DEF"/>
    </module>
    <module name="NonEmptyAtclauseDescription"/>
    <module name="JavadocTagContinuationIndentation"/>
    <module name="SummaryJavadoc">
      <property name="forbiddenSummaryFragments" value="^@return the *|^This method returns |^A [{]@code [a-zA-Z0-9]+[}]( is a )"/>
    </module>
    <module name="JavadocParagraph"/>
    <module name="AtclauseOrder">
      <property name="tagOrder" value="@param, @return, @throws, @deprecated"/>
      <property name="target" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, VARIABLE_DEF"/>
    </module>
    <module name="MethodName">
      <property name="format" value="^[a-z_][a-zA-Z0-9_]*$"/>
      <message key="name.invalidPattern" value="Method name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="SingleLineJavadoc">
      <property name="severity" value="ignore"/>
      <property name="ignoreInlineTags" value="false"/>
      <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
    </module>
    <module name="EmptyCatchBlock">
      <property name="exceptionVariableName" value="expected"/>
    </module>
    <module name="CommentsIndentation"/>
    <module name="SuppressionCommentFilter"/>
    <module name="JavadocMethod"/>
    <module name="JavadocMissingWhitespaceAfterAsterisk"/>
    <module name="JavadocStyle">
      <property name="tokens" value="ANNOTATION_DEF,ANNOTATION_FIELD_DEF,CLASS_DEF,CTOR_DEF,ENUM_CONSTANT_DEF,ENUM_DEF,INTERFACE_DEF,METHOD_DEF,VARIABLE_DEF"/>
    </module>
    <module name="MissingJavadocMethod">
      <property name="allowMissingPropertyJavadoc" value="true"/>
      <property name="allowedAnnotations" value="Before,Test,After,Override"/>
      <property name="ignoreMethodNamesRegex" value="doc|set.+|build"/>
      <property name="tokens" value="METHOD_DEF"/>
    </module>
    <module name="VisibilityModifier">
      <property name="allowPublicFinalFields" value="true"/>
      <property name="allowPublicImmutableFields" value="true"/>
      <property name="protectedAllowed" value="true"/>
    </module>
  </module>
  <module name="LineLength">
    <property name="severity" value="ignore"/>
    <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
    <property name="max" value="140"/>
    <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
  </module>
  <module name="FileTabCharacter">
    <property name="severity" value="ignore"/>
    <property name="eachLine" value="true"/>
    <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
  </module>
</module>
