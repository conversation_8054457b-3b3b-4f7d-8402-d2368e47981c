# Configure Repositories
-plugin.6.Central: \
	aQute.bnd.repository.maven.pom.provider.BndPomRepository; \
		releaseUrls=https://repo.maven.apache.org/maven2/; \
		pom=${build}/pom.xml; \
		readOnly=true; \
		name="Maven Central"

-plugin.8.Templates: \
	aQute.bnd.deployer.repository.LocalIndexedRepo; \
		name = Templates; \
		pretty = true; \
		local = ${build}/templates

-plugin.9.Release: \
	aQute.bnd.deployer.repository.LocalIndexedRepo; \
		name = Release; \
		pretty = true; \
		local = ${build}/release

-releaserepo: Release
-baselinerepo: Release

# Always use contracts
-contract: *

# JUnit
junit: \
	org.apache.servicemix.bundles.junit;version=4.12

# OpenEMS bnd.bnd defaults
buildpath: \
	osgi.annotation;version='8.1.0',\
	osgi.core;version='8.0.0',\
	org.ops4j.pax.logging.pax-logging-api;version='2.2.1',\
	org.osgi.service.cm;version='1.6.1',\
	org.osgi.service.component;version='1.5.0',\
	org.osgi.service.component.annotations;version='1.5.0',\
	org.osgi.service.event;version='1.4.1',\
	org.osgi.service.metatype;version='1.4.1',\
	org.osgi.service.metatype.annotations;version='1.4.1',\
	org.osgi.util.promise;version='1.2.0',\
	com.google.guava;version='33.4.8.jre',\
	com.google.guava.failureaccess;version='1.0.2',\
	com.google.gson;version='2.13.1',\

testpath: \
	slf4j.simple,\
	\${junit}

# OpenEMS Eclipse IDE Workingsets
-workingset =  \
	Backend;member=${filter;${p};io\.openems\.backend\..*},\
	Common;member=${filter;${p};cnf|io\.openems\.common|io\.openems\.shared\.influxdb|io\.openems\.wrapper},\
	Edge_Common;member=${filter;${p};io\.openems\.edge\.core|io\.openems\.edge\.application|io\.openems\.edge\.common|io\.openems\.edge\.simulator|io\.openems\.edge\.controller\.api|io\.openems\.edge\.edge2edge|io\.openems\.edge\.oem\..*},\
	Edge_Battery_Inverter;member=${filter;${p};io\.openems\.edge\.batteryinverter\..*},\
	Edge_Bridge;member=${filter;${p};io\.openems\.edge\.bridge\..*},\
	Edge_Battery;member=${filter;${p};io\.openems\.edge\.battery\..*},\
	Edge_Controller_Api;member=${filter;${p};io\.openems\.edge\.controller\.api\..*},\
	Edge_Controller_Debug;member=${filter;${p};io\.openems\.edge\.controller\.debug\..*},\
	Edge_Controller_Ess;member=${filter;${p};io\.openems\.edge\.controller\.asymmetric\..*|io\.openems\.edge\.controller\.symmetric\..*|io\.openems\.edge\.controller\.ess\..*},\
	Edge_Controller_Evcs;member=${filter;${p};io\.openems\.edge\.controller\.evcs.*},\
	Edge_Controller_Generic;member=${filter;${p};io\.openems\.edge\.controller\.generic\..*},\
	Edge_Controller_IO;member=${filter;${p};io\.openems\.edge\.controller\.io\..*},\
	Edge_Controller_PVinverter;member=${filter;${p};io\.openems\.edge\.controller\.pvinverter\..*},\
	Edge_Ess;member=${filter;${p};io\.openems\.edge\.ess\..*},\
	Edge_Evcs;member=${filter;${p};io\.openems\.edge\.evcs\..*|io\.openems\.wrapper\.eu\.chargetime\.ocpp},\
	Edge_Evse;member=${filter;${p};io\.openems\.edge\.evse\..*|io\.openems\.edge\.controller\.evse},\
	Edge_Multiple;member=${filter;${p};io\.openems\.edge\.fenecon\..*|io\.openems\.edge\.goodwe|io\.openems\.edge\.kostal\.piko|io\.openems\.edge\.tesla\..*|io\.openems\.edge\.solaredge|io\.openems\.edge\.bosch\..*|io\.openems\.edge\.katek\..*|io\.openems\.edge\.kaco\..*},\
	Edge_IO;member=${filter;${p};io\.openems\.edge\.io\..*|io\.openems\.edge\.controller\.channelthreshold|io\.openems\.edge\.controller\.chp\..*|io\.openems\.edge\.controller\.highloadtimeslot},\
	Edge_Meter;member=${filter;${p};io\.openems\.edge\.meter\..*},\
	Edge_Predictor;member=${filter;${p};io\.openems\.edge\.predictor\..*},\
	Edge_PVinverter;member=${filter;${p};io\.openems\.edge\.pvinverter\..*},\
	Edge_Scheduler;member=${filter;${p};io\.openems\.edge\.scheduler\..*},\
	Edge_System;member=${filter;${p};io\.openems\.edge\.system\..*},\
	Edge_Thermometer;member=${filter;${p};io\.openems\.edge\.onewire\.thermometer|io\.openems\.edge\.thermometer\..*},\
	Edge_Timedata;member=${filter;${p};io\.openems\.edge\.timedata\..*},\
	Edge_TimeOfUseTariff;member=${filter;${p};io\.openems\.edge\.timeofusetariff\..*},\

javac.source: 21
javac.target: 21
