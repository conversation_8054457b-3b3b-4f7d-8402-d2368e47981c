<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>local</groupId>
	<artifactId>central</artifactId>
	<version>0.0.0</version>
	<packaging>pom</packaging>
	<dependencies>
		<!-- biz -->
		<dependency>
			<!-- Bnd Gradle Plugin For The Bnd Workspace -->
			<!-- On update: also update gradle.properties file -->
			<groupId>biz.aQute.bnd</groupId>
			<artifactId>biz.aQute.bnd.gradle</artifactId>
			<version>7.1.0</version>
		</dependency>
		<!-- com -->
		<dependency>
			<groupId>com.fazecast</groupId>
			<artifactId>jSerialComm</artifactId>
			<version>2.9.3</version>
		</dependency>
		<dependency>
			<!-- Changelog: https://github.com/steveohara/j2mod/blob/master/RELEASE_NOTES.md -->
			<groupId>io.openems</groupId>
			<artifactId>j2mod</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.github.rzymek</groupId>
			<artifactId>opczip</artifactId>
			<version>1.2.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.13.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>33.4.8-jre</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>failureaccess</artifactId>
			<version>1.0.3</version>
		</dependency>
		<dependency>
			<!-- Used by com.influxdb: influxdb -->
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>logging-interceptor</artifactId>
			<version>5.1.0</version>
		</dependency>
		<dependency>
			<!-- Used by com.influxdb: influxdb -->
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp-jvm</artifactId>
			<version>5.1.0</version>
		</dependency>
		<dependency>
			<!-- Used by com.squareup.okhttp3: okhttp -->
			<groupId>com.squareup.okio</groupId>
			<artifactId>okio-jvm</artifactId>
			<version>3.16.0</version>
		</dependency>
		<dependency>
			<!-- Used by com.influxdb: influxdb-client -->
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>converter-gson</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<!-- Used by com.influxdb: influxdb-client -->
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>retrofit</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<!-- Used by com.influxdb: influxdb-client -->
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>converter-scalars</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<!-- Used by com.influxdb: influxdb -->
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>adapter-rxjava3</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<!-- JavaBeans Activation Framework -->
			<groupId>com.sun.activation</groupId>
			<artifactId>javax.activation</artifactId>
			<version>1.2.0</version>
		</dependency>
		<dependency>
			<!-- HikariCP A solid, high-performance, JDBC connection pool at last. -->
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
			<version>7.0.0</version>
		</dependency>
		<!-- de -->
		<dependency>
			<!-- PgBulkInsert is a Java library for Bulk Inserts to PostgreSQL using the Binary COPY Protocol. -->
			<!-- Changelog: https://github.com/PgBulkInsert/PgBulkInsert/blob/master/CHANGELOG.md-->
			<groupId>de.bytefish</groupId>
			<artifactId>pgbulkinsert</artifactId>
			<version>8.1.5</version>
		</dependency>
		<!-- eu -->
		<dependency>
			<!-- Used by io.openems.edge.evcs.ocpp. -->
			<groupId>eu.chargetime.ocpp</groupId>
			<artifactId>common</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<!-- Used by io.openems.edge.evcs.ocpp. -->
			<groupId>eu.chargetime.ocpp</groupId>
			<artifactId>OCPP-J</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<!-- Used by io.openems.edge.evcs.ocpp. -->
			<groupId>eu.chargetime.ocpp</groupId>
			<artifactId>v1_6</artifactId>
			<version>1.1.0</version>
		</dependency>
		<!-- fr -->
		<dependency>
			<groupId>fr.turri</groupId>
			<artifactId>aXMLRPC</artifactId>
			<version>1.17.0</version>
		</dependency>
		<!-- info -->
		<dependency>
			<groupId>info.faljse</groupId>
			<artifactId>SDNotify</artifactId>
			<version>1.6</version>
		</dependency>
		<!-- io -->
		<dependency>
			<!-- Miscellaneous JNA utilities for Linux  -->
			<!-- Used by io.openems.edge.io.i2c -->
			<groupId>io.helins</groupId>
			<artifactId>linux-common</artifactId>
			<version>0.1.4</version>
		</dependency>
		<dependency>
			<!-- Use the standard Linux I2C API from JVM -->
			<!-- Used by io.openems.edge.io.i2c -->
			<groupId>io.helins</groupId>
			<artifactId>linux-i2c</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<!-- Basic Linux IO utilities for java through JNA -->
			<!-- Used by io.openems.edge.io.i2c -->
			<groupId>io.helins</groupId>
			<artifactId>linux-io</artifactId>
			<version>0.0.4</version>
		</dependency>
		<dependency>
			<!-- Java utilities for errno -->
			<!-- Used by io.openems.edge.io.i2c -->
			<groupId>io.helins</groupId>
			<artifactId>linux-errno</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>io.reactivex.rxjava3</groupId>
			<artifactId>rxjava</artifactId>
			<version>3.1.11</version>
		</dependency>
		<dependency>
			<!-- Jenetics - Java Genetic Algorithm Library -->
			<!-- Release notes: https://github.com/jenetics/jenetics/blob/master/RELEASE_NOTES.md -->
			<groupId>io.jenetics</groupId>
			<artifactId>jenetics</artifactId>
			<version>8.2.0</version>
		</dependency>
		<!-- javax -->
		<dependency>
			<groupId>javax.jmdns</groupId>
			<artifactId>jmdns</artifactId>
			<version>3.4.1</version>
		</dependency>
		<!-- net -->
		<dependency>
			<!-- Used by SDNotify -->
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>5.17.0</version>
		</dependency>
		<!-- org -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>3.6.1</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Configuration Admin Service -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/configadmin/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.configadmin</artifactId>
			<version>1.9.26</version>
		</dependency>
		<dependency>
			<!-- Apache Felix EventAdmin -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/eventadmin/impl/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.eventadmin</artifactId>
			<version>1.6.4</version>
		</dependency>
		<dependency>
			<!-- Apache Felix File Install -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/fileinstall/doc/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.fileinstall</artifactId>
			<version>3.7.4</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Framework -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/framework/doc/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.framework</artifactId>
			<version>7.0.5</version>
		</dependency>
		<dependency>
			<!-- Apache Felix HTTP Service -->
			<!-- Changelog: https://github.com/apache/felix-dev/commits/master/http -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.http.jetty12</artifactId>
			<version>1.0.36</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Servlet API -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.http.servlet-api</artifactId>
			<version>6.1.0</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Inventory -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/inventory/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.inventory</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Metatype Service -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/metatype/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.metatype</artifactId>
			<version>1.2.4</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Service Component Runtime (SCR) -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/scr/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.scr</artifactId>
			<version>2.2.12</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Web Management Console -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/webconsole/README.md#releases -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.webconsole</artifactId>
			<version>5.0.12</version>
		</dependency>
		<dependency>
			<!-- Apache Felix Web Console Service Component Runtime/Declarative Services Plugin -->
			<!-- Changelog: https://github.com/apache/felix-dev/blob/master/webconsole-plugins/ds/changelog.txt -->
			<groupId>org.apache.felix</groupId>
			<artifactId>org.apache.felix.webconsole.plugins.ds</artifactId>
			<version>2.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.servicemix.bundles</groupId>
			<artifactId>org.apache.servicemix.bundles.junit</artifactId>
			<version>4.13.2_1</version>
		</dependency>
		<dependency>
			<!-- Bouncycastle for Eclipse Paho MQTTv5 Client -->
			<!-- https://mvnrepository.com/artifact/org.bouncycastle/bcpkix-jdk18on -->
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk18on</artifactId>
			<version>1.81</version>
		</dependency>
		<dependency>
			<!-- Bouncycastle for Eclipse Paho MQTTv5 Client -->
			<!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk18on -->
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk18on</artifactId>
			<version>1.81</version>
		</dependency>
		<dependency>
			<groupId>org.dhatim</groupId>
			<artifactId>fastexcel</artifactId>
			<version>0.19.0</version>
		</dependency>
		<dependency>
			<groupId>org.dhatim</groupId>
			<artifactId>fastexcel-reader</artifactId>
			<version>0.19.0</version>
		</dependency>
		<dependency>
			<!-- Eclipse Paho MQTTv5 Client -->
			<groupId>org.eclipse.paho</groupId>
			<artifactId>org.eclipse.paho.mqttv5.client</artifactId>
			<version>1.2.5</version>
		</dependency>
		<dependency>
			<groupId>com.influxdb</groupId>
			<artifactId>influxdb-client-java</artifactId>
			<version>7.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.influxdb</groupId>
			<artifactId>influxdb-client-core</artifactId>
			<version>7.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.influxdb</groupId>
			<artifactId>influxdb-client-utils</artifactId>
			<version>7.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.influxdb</groupId>
			<artifactId>flux-dsl</artifactId>
			<version>7.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.java-websocket</groupId>
			<artifactId>Java-WebSocket</artifactId>
			<version>1.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-osgi-bundle</artifactId>
			<version>2.2.0</version>
		</dependency>
		<dependency>
			<groupId>org.jetbrains.kotlinx</groupId>
			<artifactId>kotlinx-coroutines-core-jvm</artifactId>
			<version>1.10.2</version>
		</dependency>
		<dependency>
			<!-- HTML processing -->
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.21.1</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>osgi.annotation</artifactId>
			<version>8.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>osgi.core</artifactId>
			<version>8.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.framework</artifactId>
			<version>1.10.0</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.service.cm</artifactId>
			<version>1.6.1</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.service.component</artifactId>
			<version>1.5.1</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.service.component.annotations</artifactId>
			<version>1.5.1</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.service.event</artifactId>
			<version>1.4.1</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.service.metatype</artifactId>
			<version>1.4.1</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.service.metatype.annotations</artifactId>
			<version>1.4.1</version>
		</dependency>
		<dependency>
			<groupId>org.openmuc</groupId>
			<artifactId>jmbus</artifactId>
			<version>3.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.ops4j.pax.logging</groupId>
			<artifactId>pax-logging-api</artifactId>
			<version>2.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.ops4j.pax.logging</groupId>
			<artifactId>pax-logging-log4j2</artifactId>
			<version>2.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.service.jdbc</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.util.function</artifactId>
			<version>1.2.0</version>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.util.promise</artifactId>
			<version>1.3.0</version>
		</dependency>
		<dependency>
			<!-- Used by Apache Felix Web Management Console -->
			<groupId>org.owasp.encoder</groupId>
			<artifactId>encoder</artifactId>
			<version>1.3.1</version>
		</dependency>
		<dependency>
			<!-- Used by io.openems.backend.metadata.odoo -->
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.7.7</version>
		</dependency>
		<dependency>
			<!-- Used by com.influxdb: influxdb -->
			<groupId>org.reactivestreams</groupId>
			<artifactId>reactive-streams</artifactId>
			<version>1.0.4</version>
		</dependency>
		<dependency>
			<!-- Used by io.openems.edge.timedata.rrd4j -->
			<groupId>org.rrd4j</groupId>
			<artifactId>rrd4j</artifactId>
			<version>3.9</version>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>prometheus-metrics-core</artifactId>
			<version>1.3.8</version>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>prometheus-metrics-instrumentation-jvm</artifactId>
			<version>1.3.8</version>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>prometheus-metrics-exporter-httpserver</artifactId>
			<version>1.3.2</version>
		</dependency>
		<dependency>
			<!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java -->
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>4.31.1</version>
		</dependency>
	</dependencies>
</project>
