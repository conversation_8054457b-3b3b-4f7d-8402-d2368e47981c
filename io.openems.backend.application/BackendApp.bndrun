-runfw: org.apache.felix.framework;version='[7.0.5,7.0.5]'
-runee: JavaSE-21
-runprovidedcapabilities: ${native_capability}

-resolve.effective: active

# 'file.encoding=UTF-8' is required because of https://github.com/gturri/aXMLRPC/issues/43
-runvm: \
	-Dfile.encoding=UTF-8

-runproperties: \
	org.osgi.service.http.port=8079,\
	felix.cm.dir=c:/openems-backend/config,\
	openems.data.dir=c:/openems-backend/data,\
	org.apache.felix.eventadmin.Timeout=0,\
	org.ops4j.pax.logging.DefaultServiceLog.level=INFO

-runsystempackages: \
	sun.misc

-runblacklist:\
	bnd.identity;id='org.osgi.service.cm',\

-runbundles+: \
	org.apache.felix.scr;startlevel=10,\
	org.apache.felix.configadmin;startlevel=11,\
	org.apache.felix.eventadmin;startlevel=12,\
	org.apache.felix.fileinstall;startlevel=13,\
	org.ops4j.pax.logging.pax-logging-api;startlevel=14,\
	org.ops4j.pax.logging.pax-logging-log4j2;startlevel=15,\
	io.openems.backend.application;startlevel=16,\

-runrequires: \
	bnd.identity;id='org.ops4j.pax.logging.pax-logging-api',\
	bnd.identity;id='org.ops4j.pax.logging.pax-logging-log4j2',\
	bnd.identity;id='org.osgi.service.jdbc',\
	bnd.identity;id='org.apache.felix.http.jetty12',\
	bnd.identity;id='org.apache.felix.http.servlet-api',\
	bnd.identity;id='org.apache.felix.webconsole',\
	bnd.identity;id='org.apache.felix.webconsole.plugins.ds',\
	bnd.identity;id='org.apache.felix.inventory',\
	bnd.identity;id='org.apache.felix.eventadmin',\
	bnd.identity;id='org.apache.felix.fileinstall',\
	bnd.identity;id='org.apache.felix.metatype',\
	bnd.identity;id='io.openems.backend.alerting',\
	bnd.identity;id='io.openems.backend.application',\
	bnd.identity;id='io.openems.backend.b2brest',\
	bnd.identity;id='io.openems.backend.b2bwebsocket',\
	bnd.identity;id='io.openems.backend.common',\
	bnd.identity;id='io.openems.backend.core',\
	bnd.identity;id='io.openems.backend.edgewebsocket',\
	bnd.identity;id='io.openems.backend.metadata.dummy',\
	bnd.identity;id='io.openems.backend.metadata.file',\
	bnd.identity;id='io.openems.backend.metadata.odoo',\
	bnd.identity;id='io.openems.backend.metrics.prometheus',\
	bnd.identity;id='io.openems.backend.simulator',\
	bnd.identity;id='io.openems.backend.timedata.aggregatedinflux',\
	bnd.identity;id='io.openems.backend.timedata.dummy',\
	bnd.identity;id='io.openems.backend.timedata.influx',\
	bnd.identity;id='io.openems.backend.timedata.timescaledb',\
	bnd.identity;id='io.openems.backend.uiwebsocket',\

-runbundles: \
	Java-WebSocket;version='[1.6.0,1.6.1)',\
	com.fasterxml.aalto-xml;version='[1.3.3,1.3.4)',\
	com.google.gson;version='[2.13.1,2.13.2)',\
	com.google.guava;version='[33.4.8,33.4.9)',\
	com.google.guava.failureaccess;version='[1.0.3,1.0.4)',\
	com.google.protobuf;version='[4.31.1,4.31.2)',\
	com.squareup.okhttp3;version='[5.1.0,5.1.1)',\
	com.squareup.okhttp3.logging;version='[5.1.0,5.1.1)',\
	com.squareup.okio;version='[3.16.0,3.16.1)',\
	com.zaxxer.HikariCP;version='[7.0.0,7.0.1)',\
	io.openems.backend.alerting;version=snapshot,\
	io.openems.backend.application;version=snapshot,\
	io.openems.backend.b2brest;version=snapshot,\
	io.openems.backend.b2bwebsocket;version=snapshot,\
	io.openems.backend.common;version=snapshot,\
	io.openems.backend.core;version=snapshot,\
	io.openems.backend.edgewebsocket;version=snapshot,\
	io.openems.backend.metadata.dummy;version=snapshot,\
	io.openems.backend.metadata.file;version=snapshot,\
	io.openems.backend.metadata.odoo;version=snapshot,\
	io.openems.backend.metrics.prometheus;version=snapshot,\
	io.openems.backend.simulator;version=snapshot,\
	io.openems.backend.timedata.aggregatedinflux;version=snapshot,\
	io.openems.backend.timedata.dummy;version=snapshot,\
	io.openems.backend.timedata.influx;version=snapshot,\
	io.openems.backend.timedata.timescaledb;version=snapshot,\
	io.openems.backend.uiwebsocket;version=snapshot,\
	io.openems.common;version=snapshot,\
	io.openems.oem.openems;version=snapshot,\
	io.openems.shared.influxdb;version=snapshot,\
	io.openems.wrapper.aXMLRPC;version=snapshot,\
	io.openems.wrapper.fastexcel;version=snapshot,\
	io.openems.wrapper.gson;version=snapshot,\
	io.openems.wrapper.influxdb-client-core;version=snapshot,\
	io.openems.wrapper.influxdb-client-java;version=snapshot,\
	io.openems.wrapper.influxdb-client-utils;version=snapshot,\
	io.openems.wrapper.influxdb-flux-dsl;version=snapshot,\
	io.openems.wrapper.io.reactivex.rxjava3.rxjava;version=snapshot,\
	io.openems.wrapper.kotlinx-coroutines-core-jvm;version=snapshot,\
	io.openems.wrapper.okhttp;version=snapshot,\
	io.openems.wrapper.opczip;version=snapshot,\
	io.openems.wrapper.pgbulkinsert;version=snapshot,\
	io.openems.wrapper.retrofit-adapter-rxjava3;version=snapshot,\
	io.openems.wrapper.retrofit-converter-gson;version=snapshot,\
	io.openems.wrapper.retrofit-converter-scalars;version=snapshot,\
	io.openems.wrapper.retrofit2;version=snapshot,\
	io.prometheus.metrics-config;version='[1.3.8,1.3.9)',\
	io.prometheus.metrics-core;version='[1.3.8,1.3.9)',\
	io.prometheus.metrics-exporter-common;version='[1.3.2,1.3.3)',\
	io.prometheus.metrics-exporter-httpserver;version='[1.3.2,1.3.3)',\
	io.prometheus.metrics-exposition-formats;version='[1.3.2,1.3.3)',\
	io.prometheus.metrics-instrumentation-jvm;version='[1.3.8,1.3.9)',\
	io.prometheus.metrics-model;version='[1.3.8,1.3.9)',\
	io.prometheus.metrics-tracer-common;version='[1.3.8,1.3.9)',\
	io.prometheus.metrics-tracer-initializer;version='[1.3.8,1.3.9)',\
	io.prometheus.metrics-tracer-otel;version='[1.3.8,1.3.9)',\
	io.prometheus.metrics-tracer-otel-agent;version='[1.3.8,1.3.9)',\
	io.reactivex.rxjava3.rxjava;version='[3.1.11,3.1.12)',\
	org.apache.commons.commons-codec;version='[1.17.1,1.17.2)',\
	org.apache.commons.commons-compress;version='[1.27.1,1.27.2)',\
	org.apache.commons.commons-csv;version='[1.11.0,1.11.1)',\
	org.apache.commons.commons-io;version='[2.19.0,2.19.1)',\
	org.apache.felix.configadmin;version='[1.9.26,1.9.27)',\
	org.apache.felix.eventadmin;version='[1.6.4,1.6.5)',\
	org.apache.felix.fileinstall;version='[3.7.4,3.7.5)',\
	org.apache.felix.http.jetty12;version='[1.0.36,1.0.37)',\
	org.apache.felix.http.servlet-api;version='[6.1.0,6.1.1)',\
	org.apache.felix.inventory;version='[2.0.0,2.0.1)',\
	org.apache.felix.metatype;version='[1.2.4,1.2.5)',\
	org.apache.felix.scr;version='[2.2.12,2.2.13)',\
	org.apache.felix.webconsole;version='[5.0.12,5.0.13)',\
	org.apache.felix.webconsole.plugins.ds;version='[2.3.0,2.3.1)',\
	org.jetbrains.kotlin.osgi-bundle;version='[2.2.0,2.2.1)',\
	org.jsr-305;version='[3.0.2,3.0.3)',\
	org.ops4j.pax.logging.pax-logging-api;version='[2.3.0,2.3.1)',\
	org.ops4j.pax.logging.pax-logging-log4j2;version='[2.3.0,2.3.1)',\
	org.osgi.service.component;version='[1.5.1,1.5.2)',\
	org.osgi.service.jdbc;version='[1.1.0,1.1.1)',\
	org.osgi.util.function;version='[1.2.0,1.2.1)',\
	org.osgi.util.promise;version='[1.3.0,1.3.1)',\
	org.owasp.encoder;version='[1.3.1,1.3.2)',\
	org.postgresql.jdbc;version='[42.7.7,42.7.8)',\
	reactive-streams;version='[1.0.4,1.0.5)',\
	stax2-api;version='[4.2.2,4.2.3)'