plugins {
	id 'com.github.node-gradle.node'
}

node {
	nodeProjectDir = file("${projectDir}/build")
}

/*
 * Build Antora docs
 */
task buildAntoraDocs(type: NodeTask) {
	dependsOn npmInstall
	script = file("build/node_modules/@antora/cli/bin/antora")
	args = ["--log-format=pretty", "site.yml"]

	def nojekyll = file("build/.nojekyll")
	def source = file("build/CNAME")
	def output = file("build/www")

	doLast {
		copy {
			from nojekyll, source
			into output
		}
	}
}
