* xref:introduction.adoc[Introduction]
* xref:gettingstarted.adoc[Getting Started]
* xref:coreconcepts.adoc[Core concepts & terminology]
* OpenEMS Edge
** xref:edge/architecture.adoc[Architecture]
** xref:edge/configuration.adoc[Configuration]
** xref:edge/nature.adoc[Nature]
** xref:edge/scheduler.adoc[Scheduler]
** xref:edge/controller.adoc[Controller]
** xref:edge/bridge.adoc[Bridge]
** xref:edge/device_service.adoc[Device & Service]
** xref:edge/timedata.adoc[Timedata]
** xref:edge/implement.adoc[Implementing a Device]
** xref:edge/build.adoc[Build OpenEMS Edge]
** Deploy
*** xref:edge/deploy/systemd.adoc[Deploy on Debian/Ubuntu]
*** xref:edge/deploy/docker.adoc[Deploy to Docker]

* OpenEMS UI
** xref:ui/setup-ide.adoc[Setup IDE]
** xref:ui/architecture.adoc[Architecture]
** xref:ui/build.adoc[Build OpenEMS UI]
*** xref:ui/implementing-a-widget/introduction.adoc[Implementing a UI Widget]
**** xref:ui/implementing-a-widget/components/flat.adoc[Flat-Widget]
**** xref:ui/implementing-a-widget/components/modal.adoc[Modal-Widget]
**** xref:ui/implementing-a-widget/components/chart.adoc[Chart]

* OpenEMS Backend
** xref:backend/architecture.adoc[Architecture]
** xref:backend/backend-to-backend.adoc[Backend-to-Backend]
** xref:backend/metadata.adoc[Metadata]
** xref:backend/timedata.adoc[Timedata]
** xref:backend/service.adoc[Service]
** Deploy
*** xref:backend/deploy/systemd.adoc[Deploy on Debian/Ubuntu]
*** xref:backend/deploy/docker.adoc[Deploy to Docker]

* xref:component-communication/index.adoc[Component Communication]
* xref:logging/log4j2.adoc[Logging]
* Simulation
** xref:simulation/gitpod.adoc[Live-Demo on Gitpod]
** xref:simulation/realtime.adoc[Real-Time Simulation]
** xref:simulation/ui-history.adoc[UI History Simulation]
* Contribute
** xref:contribute/coding-guidelines.adoc[Coding Guidelines]
** xref:contribute/documentation.adoc[Documentation]
* https://openems.github.io/openems.io/javadoc/[Javadoc]
