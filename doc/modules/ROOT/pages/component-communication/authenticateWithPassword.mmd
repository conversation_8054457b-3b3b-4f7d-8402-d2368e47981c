sequenceDiagram

participant IndexComponent as UI:IndexComponent<br/><br/>/app/index/<br/>index.component.ts
participant Websocket as UI:Websocket<br/><br/>/app/shared/service/<br/>websocket.ts & wsdata.ts
participant WebsocketApi as Edge:WebsocketApi<br/><br/>controller.api.websocket<br/>OnRequest.java

IndexComponent ->> IndexComponent: doLogin()  
note over IndexComponent: create <br/>AuthenticateWith-<br/>PasswordRequest
IndexComponent ->> Websocket: sendRequest(): promise
activate Websocket
Websocket ->> WebsocketApi: run()
activate WebsocketApi
WebsocketApi ->> WebsocketApi: handleEdgeRpcRequest()
WebsocketApi ->> WebsocketApi: handleAuthenticateWithPasswordRequest()
note over WebsocketApi : create <br/>AuthenticateWith-<br/>PasswordResponse
WebsocketApi ->> Websocket: handleJsonrpcResponse()
deactivate WebsocketApi
Websocket ->> IndexComponent: resolve promise
deactivate Websocket
IndexComponent ->> IndexComponent: handleAuthenticationWithPasswordResponse()