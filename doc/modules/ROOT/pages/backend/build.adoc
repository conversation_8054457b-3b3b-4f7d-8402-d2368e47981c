= Build OpenEMS Backend
:sectnums:
:sectnumlevels: 4
:toclevels: 4
:experimental:
:keywords: AsciiDoc
:source-highlighter: highlight.js
:icons: font
:imagesdir: ../../assets/images

This chapter explains, how the OpenEMS Backend can be compiled to a JAR file that can be executed outside of an IDE.

== Build using Eclipse IDE

. Inside Eclipse IDE open the *io.openems.backend.application* project and open the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.application/BackendApp.bndrun[BackendApp.bndrun icon:code[]] file.

. Press btn:[Export] to start the *Export Wizard Selection* assistant.

. Select btn:[Executable JAR] and press btn:[Next >].

. Select a *Destination* for *Export to JAR*.

. Press btn:[Finish]

This creates a so called Fat-JAR-file including all bundles. It can be executed by running `java -jar openems-backend.jar` in a console.

== Build using Gradle from command line

link:https://gradle.org/[Gradle] is a build tool that is used in the OpenEMS project to compile the JAR-files and to execute other tasks like building the documentation webpage using link:https://antora.org/[Antora] and the Javadocs. To build OpenEMS Backend:

. Open a console and change to your repository directory.

. Execute `gradlew buildBackend`

=== Building only a specific project

If developing a component for the backend, it might make sense to only build the component in isolation instead of the entire backend (same applies to the Edge). This can be achieved by executing `gradlew :io.openems.backend.metadata.mymetadata:build`, which produces a JAR file.
