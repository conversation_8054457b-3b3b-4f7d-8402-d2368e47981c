# SOME DESCRIPTIVE TITLE
# Copyright (C) YEAR Free Software Foundation, Inc.
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: 2019-06-03 22:41+0200\n"
"PO-Revision-Date: 2019-06-05 08:59+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.3\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. type: Title =
#: backend/architecture.adoc:1
#, no-wrap
msgid "Backend Architecture"
msgstr "Backend Architektur"

#. type: Title =
#: backend/backend-to-backend.adoc:1
#, no-wrap
msgid "Backend-to-Backend communication"
msgstr "Backend-zu-Backend Kommunikation"

#. type: Plain text
#: backend/backend-to-backend.adoc:13
msgid "OpenEMS Backend provides two \"Backend-to-Backend-Api\" implementations, that provide a way to communicate with one or more OpenEMS Edge devices via backend connection. They are designed using the JSON-RPC communication protocol via REST/JSON (_io.openems.backend.b2brest_) or via Websocket (_io.openems.backend.b2bwebsocket_) connection."
msgstr "OpenEMS Backend bietet zwei \"Backend-to-Backend-Api\"-Implementierungen, die eine Möglichkeit bieten, mit einem oder mehreren OpenEMS-Edge-Geräten über Backend-Verbindungen zu kommunizieren. Diese werden mit dem JSON-RPC-Kommunikationsprotokoll über REST/JSON (_io.openems.backend.b2brest_) oder über die Websocket (_io.openems.backend.b2bwebsocket_) Verbindung entwickelt."

#. type: Title ==
#: backend/backend-to-backend.adoc:14
#, no-wrap
msgid "Authentication"
msgstr "Authentifizierung"

#. type: Plain text
#: backend/backend-to-backend.adoc:17
msgid "BasicAuth on opening of the connection."
msgstr "BasicAuth bei der Öffnung der Verbindung."

#. type: Title ==
#: backend/backend-to-backend.adoc:18
#, no-wrap
msgid "Error Handling"
msgstr "Fehlerbehandlung"

#. type: Plain text
#: backend/backend-to-backend.adoc:22
msgid "Errors are implemented according to the specs of JSON-RPC 2.0 (https://www.jsonrpc.org/specification#response_object). Possible error codes are documented here: https://github.com/OpenEMS/openems/blob/develop/io.openems.common/src/io/openems/common/exceptions/OpenemsError.java#L13"
msgstr "Fehler werden nach den Spezifikationen von JSON-RPC 2.0 (https://www.jsonrpc.org/specification#response_object) implementiert. Mögliche Fehlercodes werden hier dokumentiert: https://github.com/OpenEMS/openems/blob/develop/io.openems.common/src/io/openems/common/exceptions/OpenemsError.java#L13"

#. type: Plain text
#: backend/backend-to-backend.adoc:24
msgid "Example:"
msgstr "Beispiel:"

#. type: delimited block -
#: backend/backend-to-backend.adoc:35
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\": \"2.0\",\n"
"  \"id\": \"UUID\",\n"
"  \"error\": {\n"
"    \"code\": 3000,\n"
"    \"message\": \"Edge [edge0] is not connected\",\n"
"    \"data: [\"edge0\"]\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\": \"2.0\",\n"
"  \"id\": \"UUID\",\n"
"  \"error\": {\n"
"    \"code\": 3000,\n"
"    \"message\": \"Edge [edge0] is not connected\",\n"
"    \"data: [\"edge0\"]\n"
"  }\n"
"}\n"

#. type: Plain text
#: backend/backend-to-backend.adoc:38
msgid "Properties `id` and `jsonrpc` can be omitted for JSON/REST, as they are not required for HTTP POST calls."
msgstr "Eigenschaften `id` und `jsonrpc` können für JSON/REST weggelassen werden, da sie für HTTP-POST-Aufrufe nicht benötigt werden."

#. type: Title ==
#: backend/backend-to-backend.adoc:39
#, no-wrap
msgid "Requests"
msgstr "Anfragen"

#. type: Title ===
#: backend/backend-to-backend.adoc:41
#, no-wrap
msgid "GetEdgesStatus"
msgstr "GetEdgesStatus"

#. type: Plain text
#: backend/backend-to-backend.adoc:44
msgid "Lists the current status of all accessible Edges."
msgstr "Listet den aktuellen Status aller zugänglichen Edges auf."

#. type: Title ====
#: backend/backend-to-backend.adoc:45 backend/backend-to-backend.adoc:80 backend/backend-to-backend.adoc:128 backend/backend-to-backend.adoc:199
#, no-wrap
msgid "Request"
msgstr "Anfrage"

#. type: delimited block -
#: backend/backend-to-backend.adoc:54
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"getEdgesStatus\",\n"
"  \"params\":{}\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"getEdgesStatus\",\n"
"  \"params\":{}\n"
"}\n"

#. type: Title ====
#: backend/backend-to-backend.adoc:56 backend/backend-to-backend.adoc:100 backend/backend-to-backend.adoc:149 backend/backend-to-backend.adoc:219
#, no-wrap
msgid "Response"
msgstr "Antwort"

#. type: delimited block -
#: backend/backend-to-backend.adoc:71
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"result\":{\n"
"    \"edge0\":{\n"
"      \"online\":true\n"
"    },\n"
"    \"edge1\":{\n"
"      \"online\":false\n"
"    }\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"result\":{\n"
"    \"edge0\":{\n"
"      \"online\":true\n"
"    },\n"
"    \"edge1\":{\n"
"      \"online\":false\n"
"    }\n"
"  }\n"
"}\n"

#. type: Title ====
#: backend/backend-to-backend.adoc:73 backend/backend-to-backend.adoc:119 backend/backend-to-backend.adoc:192 backend/backend-to-backend.adoc:229
#, no-wrap
msgid "Demo"
msgstr "Demo"

#. type: Plain text
#: backend/backend-to-backend.adoc:75
msgid "https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.b2bwebsocket/test/io/openems/backend/b2bwebsocket/B2bWebsocketTest.java: `testGetEdgesStatusRequest()`"
msgstr "https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.b2bwebsocket/test/io/openems/backend/b2bwebsocket/B2bWebsocketTest.java: ' testGetEdgesStatusRequest () '"

#. type: Title ===
#: backend/backend-to-backend.adoc:76
#, no-wrap
msgid "GetEdgesChannelsValues"
msgstr "GetEdgesChannelsValues"

#. type: Plain text
#: backend/backend-to-backend.adoc:79
msgid "Queries the latest value of certain channels for a number of Edge-Devices. Channels that are not available or do not have a current value are returned as `null`."
msgstr "Fragt den aktuellen Wert bestimmter Kanäle für eine Reihe von Edge-Geräten ab. Kanäle, die nicht verfügbar sind oder keinen aktuellen Wert haben, werden als \"null\" zurückgegeben."

#. type: delimited block -
#: backend/backend-to-backend.adoc:98
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"getEdgesChannelsValues\",\n"
"  \"params\":{\n"
"    \"ids\":[\n"
"      \"edge0\",\n"
"      \"edge1\"\n"
"    ],\n"
"    \"channels\":[\n"
"      \"_sum/EssSoc\",\n"
"      \"_sum/ProductionActivePower\"\n"
"    ]\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"getEdgesChannelsValues\",\n"
"  \"params\":{\n"
"    \"ids\":[\n"
"      \"edge0\",\n"
"      \"edge1\"\n"
"    ],\n"
"    \"channels\":[\n"
"      \"_sum/EssSoc\",\n"
"      \"_sum/ProductionActivePower\"\n"
"    ]\n"
"  }\n"
"}\n"

#. type: delimited block -
#: backend/backend-to-backend.adoc:117
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"result\":{\n"
"    \"edge0\":{\n"
"      \"_sum/EssSoc\":50,\n"
"      \"_sum/ProductionActivePower\":0\n"
"    },\n"
"    \"edge1\":{\n"
"      \"_sum/EssSoc\":99,\n"
"      \"_sum/ProductionActivePower\":null\n"
"    }\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"result\":{\n"
"    \"edge0\":{\n"
"      \"_sum/EssSoc\":50,\n"
"      \"_sum/ProductionActivePower\":0\n"
"    },\n"
"    \"edge1\":{\n"
"      \"_sum/EssSoc\":99,\n"
"      \"_sum/ProductionActivePower\":null\n"
"    }\n"
"  }\n"
"}\n"

#. type: Plain text
#: backend/backend-to-backend.adoc:121
msgid "https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.b2bwebsocket/test/io/openems/backend/b2bwebsocket/B2bWebsocketTest.java: `testGetEdgesChannelsValuesRequest()`"
msgstr "https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.b2bwebsocket/test/io/openems/backend/b2bwebsocket/B2bWebsocketTest.java: ' testGetEdgesChannelsValuesRequest () '"

#. type: Title ===
#: backend/backend-to-backend.adoc:122
#, no-wrap
msgid "SubscribeToChannels"
msgstr "SubscribeToChannels"

#. type: Plain text
#: backend/backend-to-backend.adoc:125
msgid "Registers a subscription for regular updates of channel values. Request is acknowledged by an empty success Response and followed by regular JSON-RPC Notifications. Du stop the subscription, an empty 'subscribeEdgesChannels' Request needs to be sent."
msgstr "Registriert ein Abonnement für regelmäßige Aktualisierungen der Kanalwerte. Die Anfrage wird durch eine leere Erfolgsantwort und gefolgt von regelmäßigen JSON-RPC-Benachrichtigungen bestätigt. Um das Abonnement zu stoppen, muss eine leere \"subscribeEdgesChannels\"-Anfrage gesendet werden."

#. type: Plain text
#: backend/backend-to-backend.adoc:127
msgid "The parameter \"count\" must be increased with each new Request. Only the Request with the highest \"count\" value is active."
msgstr "Der Parameter \"count\" muss mit jeder neuen Anfrage erhöht werden. Nur die Anfrage mit dem höchsten \"count\" Wert ist aktiv."

#. type: delimited block -
#: backend/backend-to-backend.adoc:147
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"subscribeEdgesChannels\",\n"
"  \"params\":{\n"
"    \"count\": 0\n"
"    \"ids\":[\n"
"      \"edge0\",\n"
"      \"edge1\"\n"
"    ],\n"
"    \"channels\":[\n"
"      \"_sum/EssSoc\",\n"
"      \"_sum/ProductionActivePower\"\n"
"    ]\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"subscribeEdgesChannels\",\n"
"  \"params\":{\n"
"    \"count\": 0\n"
"    \"ids\":[\n"
"      \"edge0\",\n"
"      \"edge1\"\n"
"    ],\n"
"    \"channels\":[\n"
"      \"_sum/EssSoc\",\n"
"      \"_sum/ProductionActivePower\"\n"
"    ]\n"
"  }\n"
"}\n"

#. type: delimited block -
#: backend/backend-to-backend.adoc:157 backend/backend-to-backend.adoc:227
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"result\":{}\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"result\":{}\n"
"}\n"

#. type: Title ====
#: backend/backend-to-backend.adoc:159
#, no-wrap
msgid "Notifications"
msgstr "Benachrichtigungen"

#. type: delimited block -
#: backend/backend-to-backend.adoc:176
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"method\":\"edgesCurrentData\",\n"
"  \"params\":{\n"
"    \"edge0\":{\n"
"      \"_sum/EssSoc\":50,\n"
"      \"_sum/ProductionActivePower\":1502\n"
"    },\n"
"    \"edge1\":{\n"
"      \"_sum/EssSoc\":20,\n"
"      \"_sum/ProductionActivePower\":null\n"
"    }\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"method\":\"edgesCurrentData\",\n"
"  \"params\":{\n"
"    \"edge0\":{\n"
"      \"_sum/EssSoc\":50,\n"
"      \"_sum/ProductionActivePower\":1502\n"
"    },\n"
"    \"edge1\":{\n"
"      \"_sum/EssSoc\":20,\n"
"      \"_sum/ProductionActivePower\":null\n"
"    }\n"
"  }\n"
"}\n"

#. type: Title ====
#: backend/backend-to-backend.adoc:178
#, no-wrap
msgid "Unsubscribe-Request"
msgstr "Anfrage abbestellen"

#. type: delimited block -
#: backend/backend-to-backend.adoc:190
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"subscribeEdgesChannels\",\n"
"  \"params\":{\n"
"    \"ids\":[],\n"
"    \"channels\":[]\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"subscribeEdgesChannels\",\n"
"  \"params\":{\n"
"    \"ids\":[],\n"
"    \"channels\":[]\n"
"  }\n"
"}\n"

#. type: Plain text
#: backend/backend-to-backend.adoc:194 backend/backend-to-backend.adoc:230
msgid "https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.b2bwebsocket/test/io/openems/backend/b2bwebsocket/B2bWebsocketTest.java: `testSubscribeEdgesChannelsRequest()`"
msgstr "https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.b2bwebsocket/test/io/openems/backend/b2bwebsocket/B2bWebsocketTest.java: ' testSubscribeEdgesChannelsRequest () '"

#. type: Title ===
#: backend/backend-to-backend.adoc:195
#, no-wrap
msgid "SetGridConnSchedule"
msgstr "SetGridConnSchedule"

#. type: Plain text
#: backend/backend-to-backend.adoc:198
msgid "Each battery storage system in an Edge-Device can be controlled to balance on a specific value at the grid connection point. This Request allows sending such a Schedule to an Edge-Device."
msgstr "Jedes Batteriespeicher in einem Edge-Gerät kann so gesteuert werden, dass es am Netzanschlusspunkt auf einem bestimmten Wert ausbalanciert. Diese Anfrage erlaubt es, einen solchen Zeitplan an ein Edge-Gerät zu schicken."

#. type: delimited block -
#: backend/backend-to-backend.adoc:217
#, no-wrap
msgid ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"setGridConnSchedule\",\n"
"  \"params\":{\n"
"    \"id\":\"edgeId\",\n"
"    \"schedule\":[\n"
"      {\n"
"        \"startTimestamp\":1542464697,\n"
"        \"duration\":900,\n"
"        \"activePowerSetPoint\":0\n"
"      }\n"
"    ]\n"
"  }\n"
"}\n"
msgstr ""
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\": \"UUID\",\n"
"{\n"
"  \"jsonrpc\":\"2.0\",\n"
"  \"id\":\"UUID\",\n"
"  \"method\":\"setGridConnSchedule\",\n"
"  \"params\":{\n"
"    \"id\":\"edgeId\",\n"
"    \"schedule\":[\n"
"      {\n"
"        \"startTimestamp\":1542464697,\n"
"        \"duration\":900,\n"
"        \"activePowerSetPoint\":0\n"
"      }\n"
"    ]\n"
"  }\n"
"}\n"
"\n"
"  \"Methode\": \"setGridConnSchedule\",\n"
"  \"Params\": {\n"
"    \"id\": \"EdgeId\",\n"
"    \"Zeitplan\": [\n"
"      {\n"
"        \"startTimestamp\":1542464697,\n"
"        \"duration\":900,\n"
"        \"activePowerSetPoint\":0\n"
"      }\n"
"    ]\n"
"  }\n"
"}\n"

#. type: Title =
#: backend/build.adoc:1
#, no-wrap
msgid "Build OpenEMS Backend"
msgstr "OpenEMS Backend bauen"

#. type: Plain text
#: backend/build.adoc:13 edge/build.adoc:13
msgid "This chapter explains, how OpenEMS Edge can be compiled to a JAR file that can be executed outside of an IDE."
msgstr "In diesem Kapitel wird erklärt, wie OpenEMS Edge zu einer JAR-Datei kompiliert werden kann, die außerhalb einer IDE ausgeführt werden kann."

#. type: Title ==
#: backend/build.adoc:14 edge/build.adoc:14
#, no-wrap
msgid "Build using Eclipse IDE"
msgstr "Mit Eclipse IDE bauen"

#. type: Plain text
#: backend/build.adoc:17
msgid "Inside Eclipse IDE open the *io.openems.backend.application* project and open the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.application/BackendApp.bndrun[BackendApp.bndrun icon:code[]] file."
msgstr "In der Eclipse IDE öffnen Sie das * io.openems.backend.application * Projekt und den link:https://github.com/OpenEMS/openems/blob/develop/io.openems.backend.application/BackendApp.bndrun [BackendApp.bndrun icon:code[]] file."

#. type: Plain text
#: backend/build.adoc:19 edge/build.adoc:22
msgid "Press btn:[Export] to start the *Export Wizard Selection* assistant."
msgstr "Drücken Sie [Export], um den\"Export Wizard Selection\"-Assistenten zu starten."

#. type: Plain text
#: backend/build.adoc:21 edge/build.adoc:24
msgid "Select btn:[Executable JAR] and press btn:[Next >]."
msgstr "Wählen Sie btn:[Executable JAR] und drücken Sie btn:[Next >]."

#. type: Plain text
#: backend/build.adoc:23 edge/build.adoc:29
msgid "Select a *Destination* for *Export to JAR*."
msgstr "Wählen Sie einen Zielort (*Destination*) für die exportierte JAR-Datei (*Export to JAR*)."

#. type: Plain text
#: backend/build.adoc:25 edge/build.adoc:34
msgid "Press btn:[Finish]"
msgstr "Drücken Sie btn:[Finish]"

#. type: Plain text
#: backend/build.adoc:27
msgid "This creates a so called Fat-JAR-file including all bundles. It can be executed by running `java -jar openems-backend.jar` in a console."
msgstr "Dadurch wird eine sogenannte Fat-JAR-Datei erstellt, die alle Bundles enthält. Es kann ausgeführt werden, indem man ' java-jar openems-backend.jar ' in einer Konsole startet."

#. type: Title ==
#: backend/build.adoc:28 edge/build.adoc:37
#, no-wrap
msgid "Build using Gradle from command line"
msgstr "Mit Gradle aus der Kommandozeile bauen"

#. type: Plain text
#: backend/build.adoc:31
msgid "link:https://gradle.org/[Gradle] is a build tool that is used in the OpenEMS project to compile the JAR-files and to execute other tasks like building the documentation webpage using link:https://antora.org/[Antora] and the Javadocs. To build OpenEMS Backend:"
msgstr "link:https:/gradle.org/[Gradle] ist ein Build-Tool, das im OpenEMS-Projekt verwendet wird, um die JAR-Dateien zu kompilieren und andere Aufgaben auszuführen, wie zum Beispiel das Bauen der Dokumentations-Webseite mit link:https:/antora.org/[Antora] und die Javadocs. OpenEMS-Backend zu bauen:"

#. type: Plain text
#: backend/build.adoc:33 edge/build.adoc:42
msgid "Open a console and change to your repository directory."
msgstr "Öffnen Sie eine Konsole und wechseln Sie in Ihr Projektarchiv-Verzeichnis."

#. type: Plain text
#: backend/build.adoc:34
msgid "Execute `gradlew buildBackend`"
msgstr "Führen Sie `gradlew buildBackend` aus"

#. type: Title =
#: backend/configuration.adoc:1 edge/configuration.adoc:1
#, no-wrap
msgid "Configuration"
msgstr "Konfiguration"

#. type: Title =
#: backend/deploy.adoc:1 edge/deploy.adoc:1
#, no-wrap
msgid "Deploy OpenEMS Edge"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte"

#. type: Plain text
#: backend/deploy.adoc:13
msgid "This chapter explains how OpenEMS Backend can be deployed on a Debian Linux server. Similar techniques will work for other operating systems as well."
msgstr "In diesem Kapitel wird erklärt, wie OpenEMS Backend auf einem Debian-Linux-Server eingesetzt werden kann. Ähnliche Techniken werden auch für andere Betriebssysteme funktionieren."

#. type: Title ===
#: backend/deploy.adoc:14 edge/deploy.adoc:58
#, no-wrap
msgid "Prepare operating system environment"
msgstr "Betriebssystemumgebung vorbereiten"

#. type: Plain text
#: backend/deploy.adoc:17
msgid "It is recommended to run every service on a server with limited permissions. This example runs OpenEMS Backend with user \"root\" which is a bad idea for a production server!"
msgstr "Es wird empfohlen, jeden Dienst auf einem Server mit eingeschränkten Berechtigungen auszuführen. Dieses Beispiel läuft OpenEMS Backend mit dem Benutzer \"root\", was eine schlechte Idee für einen Produktionsserver ist!"

#. type: Title ====
#: backend/deploy.adoc:18 edge/deploy.adoc:60
#, no-wrap
msgid "Create an application directory"
msgstr "Ein Anwendungsverzeichnis erstellen"

#. type: Plain text
#: backend/deploy.adoc:21
msgid "Create the directory */opt/openems-backend*. This is going to be the place, where we put the JAR file."
msgstr "Erstellen Sie das Verzeichnis /opt/openems-backend. Dies wird der Ort sein, an dem wir die JAR-Datei platzieren."

#. type: Plain text
#: backend/deploy.adoc:23
msgid "Execute `mkdir /opt/openems-backend`."
msgstr "Führen Sie die `mkdir/opt/openems-backend` aus."

#. type: Title ====
#: backend/deploy.adoc:24 edge/deploy.adoc:66
#, no-wrap
msgid "Create a config directory"
msgstr "Ein Konfigurationsverzeichnis erstellen"

#. type: Plain text
#: backend/deploy.adoc:27
msgid "Create the directory */opt/openems-backend/config.d*. This is going to be the place, where all the bundle configurations are held."
msgstr "Erstellen Sie das Verzeichnis */opt/openems-backend/config.d *. Dies wird der Ort sein, an dem alle Bündelkonfigurationen stattfinden."

#. type: Plain text
#: backend/deploy.adoc:29
msgid "Execute `mkdir /opt/openems-backend/config.d`."
msgstr "Führen Sie ' mkdir/opt/openems-backend/config.d ' aus."

#. type: Title ====
#: backend/deploy.adoc:30 edge/deploy.adoc:72
#, no-wrap
msgid "Create a systemd service definition"
msgstr "Eine systemd-Service-Definition erstellen"

#. type: Plain text
#: backend/deploy.adoc:33
msgid "The systemd 'Service Manager' manages system processes in a Debian Linux. We will create a systemd service definition file, so that systemd takes care of managing (starting/restarting/...) the OpenEMS Backend service."
msgstr "Der Systemd 'Service Manager' verwaltet Systemprozesse in einem Debian Linux. Wir werden eine Systemd-Service-Definition-Datei erstellen, so dass sich die systemd um die Verwaltung (starting/restarting/...) des OpenEMS Backend-Service kümmert."

#. type: Plain text
#: backend/deploy.adoc:35 edge/deploy.adoc:77
msgid "Create and open the service definition file."
msgstr "Erstellen und öffnen Sie die Service-Definition-Datei."

#. type: Plain text
#: backend/deploy.adoc:37
msgid "Execute `nano /etc/systemd/system/openems-backend.service`"
msgstr "Führen Sie `nano/etc/systemd/systemd/openems-backend.service` aus"

#. type: Plain text
#: backend/deploy.adoc:39 edge/deploy.adoc:81
msgid "Paste the following content:"
msgstr "Fügen Sie folgenden Inhalt ein:"

#. type: delimited block -
#: backend/deploy.adoc:44 edge/deploy.adoc:86
#, no-wrap
msgid ""
"[Unit]\n"
"Description=OpenEMS <1>\n"
"After=network.target <2>\n"
msgstr ""
"[Einheit]\n"
"Beschreibung = OpenEMS <1>\n"
"After = network.target <2>\n"

#. type: delimited block -
#: backend/deploy.adoc:54
#, no-wrap
msgid ""
"[Service]\n"
"User=root <3>\n"
"Group=root\n"
"Type=simple <4>\n"
"WorkingDirectory=/opt/openems-backend\n"
"ExecStart=/usr/bin/java -XX:+ExitOnOutOfMemoryError -Dfelix.cm.dir=/opt/openems-backend/config.d -Djava.util.concurrent.ForkJoinPool.common.parallelism=100 -jar /opt/openems-backend/openems-backend.jar <5>\n"
"SuccessExitStatus=143 <6>\n"
"Restart=always <7>\n"
"RestartSec=10 <8>\n"
msgstr ""
"[Service]\n"
"User=root <3>\n"
"Group=root\n"
"Type=simple <4>\n"
"WorkingDirectory=/opt/openems-backend\n"
"ExecStart=/usr/bin/java -XX:+ExitOnOutOfMemoryError -Dfelix.cm.dir=/opt/openems-backend/config.d -Djava.util.concurrent.ForkJoinPool.common.parallelism=100 -jar /opt/openems-backend/openems-backend.jar <5>\n"
"SuccessExitStatus=143 <6>\n"
"Restart=always <7>\n"
"RestartSec=10 <8>\n"

#. type: delimited block -
#: backend/deploy.adoc:57 edge/deploy.adoc:105
#, no-wrap
msgid ""
"[Install]\n"
"WantedBy=multi-user.target\n"
msgstr ""
"[Install]\n"
"WantedBy=multi-user.target\n"

#. type: Plain text
#: backend/deploy.adoc:59 edge/deploy.adoc:107
msgid "The name of the service."
msgstr "Der Name des Dienstes."

#. type: Plain text
#: backend/deploy.adoc:60 edge/deploy.adoc:108
msgid "The service is allowed to start after network is available (e.g. to be able to access devices via ethernet connection)"
msgstr "Der Dienst darf nach der Verfügbarkeit des Netzwerks starten (z. B. um über die Ethernet-Verbindung auf Geräte zugreifen zu können)"

#. type: Plain text
#: backend/deploy.adoc:61 edge/deploy.adoc:109
msgid "It is run as user 'root' to have access to all devices. It is recommended to change this for productive systems."
msgstr "Es wird als User ' root ' ausgeführt, um Zugriff auf alle Geräte zu haben. Es wird empfohlen, dies für produktive Systeme zu ändern."

#. type: Plain text
#: backend/deploy.adoc:62
msgid "OpenEMS Backend uses a \"simple\" process fork."
msgstr "OpenEMS Backend verwendet eine \"einfache\" Prozessgabel."

#. type: Plain text
#: backend/deploy.adoc:63
msgid "The start command. It uses the Java JRE, sets the config directory to `/opt/openems-backend/config.d`, sets a parallelism value for ForkJoinPool - this depends on the number of OpenEMS Edge devices you expect to connect - and runs the jar file at `/opt/openems-backend/openems-backend.jar`"
msgstr "Der Startbefehl. Er verwendet das Java JRE, setzt das Konfigurationsverzeichnis auf `/opt/openems-backend/config.d`, setzt einen Parallelismus-Wert für ForkJoinPool-das hängt von der Anzahl der OpenEMS Edge-Geräte ab, die Sie verbinden wollen-und läuft die jar-Datei unter '/opt/openems-backend/ Openems-backend.jar '"

#. type: Plain text
#: backend/deploy.adoc:64 edge/deploy.adoc:112
msgid "In contrast to what systemd expects, Java exits with status 143 on success."
msgstr "Im Gegensatz zu den Erwartungen von systemd wird Java bei Erfolg mit dem Status 143 beendet."

#. type: Plain text
#: backend/deploy.adoc:65
msgid "Systemd _always_ tries to restart OpenEMS Backend once it was quit."
msgstr "Systemd versucht immer OpenEMS Backend neu zu starten sobald es einmal beendet wurde."

#. type: Plain text
#: backend/deploy.adoc:66 edge/deploy.adoc:114
msgid "Systemd waits _10_ seconds till the next restart."
msgstr "Systemd wartet 10 Sekunden bis zum nächsten Neustart."

#. type: Plain text
#: backend/deploy.adoc:68 edge/deploy.adoc:117
msgid "Press btn:[Ctrl] + btn:[x] to exit and btn:[y] to save the file."
msgstr "Drücken Sie btn:[Strg] + btn:[x] zum verlassen und btn:[y] um die Datei zu speichern."

#. type: Plain text
#: backend/deploy.adoc:70 edge/deploy.adoc:119
msgid "Activate the service definition:"
msgstr "Aktivieren Sie die Service-Definition:"

#. type: Plain text
#: backend/deploy.adoc:72 edge/deploy.adoc:121
msgid "Execute `systemctl daemon-reload`"
msgstr "`systemctl daemon-reload` ausführen"

#. type: Title ===
#: backend/deploy.adoc:73
#, no-wrap
msgid "Start OpenEMS Backend"
msgstr "Starte OpenEMS Backend"

#. type: Plain text
#: backend/deploy.adoc:76
msgid "To update the OpenEMS JAR file at the target device, it is required to copy the JAR file from your build directory to `/opt/openems-backend/openems-backend.jar` on the server. Afterwards it is required to restart the systemd service"
msgstr "Um die OpenEMS-JAR-Datei auf dem Zielgerät zu aktualisieren, ist es erforderlich, die JAR-Datei aus Ihrem Build-Verzeichnis `/opt/openems-backend/openems-backend.jar` auf dem Server zu kopieren. Danach ist es erforderlich, den systemd-Dienst neu zu starten"

#. type: Plain text
#: backend/deploy.adoc:78
msgid "(Re)start OpenEMS systemd service."
msgstr "Den OpenEMS systemd Service (neu)starten"

#. type: Plain text
#: backend/deploy.adoc:80
msgid "Execute `systemctl restart openems-backend --no-block; journalctl -lfu openems-backend`"
msgstr "Führen Sie `systemctl restart openems-backend --no-block; journalctl -lfu openems-backend` aus."

#. type: Plain text
#: backend/deploy.adoc:81
msgid "The command restarts the service (_systemctl restart openems-backend_) while not waiting for the OpenEMS startup notification (_--no-block_). Then it directly prints the OpenEMS system log (_journalctl -lfu openems-backend_)."
msgstr "Der Befehl startet den Dienst neu (_ systemctl restart openems-backend _), während er nicht auf die OpenEMS-Startmitteilung (_--no-block _) wartet. Dann druckt es direkt das OpenEMS-Systemprotokoll (_ journalctl-lfu openems-backend _)."

#. type: Title =
#: component-communication/index.adoc:1
#, no-wrap
msgid "Internal Component Communication"
msgstr "Interne Komponentenkommunikation"

#. type: Plain text
#: component-communication/index.adoc:13
msgid ""
"This page describes the internal communication protocol between OpenEMS Edge, OpenEMS Backend and OpenEMS UI. The components keep an open https://de.wikipedia.org/wiki/WebSocket[Websocket] connection which is used for bi-directional communication. The protocol is based on https://www.jsonrpc.org/specification[JSON-RPC]. For details about JSON-RPC please refer to the specification. As a rough summary, the protocol is divided "
"into"
msgstr ""
"Diese Seite beschreibt das interne Kommunikationsprotokoll zwischen OpenEMS Edge, OpenEMS Backend und OpenEMS UI. Die Komponenten halten eine offene https://de.wikipedia.org/wiki/WebSocket[Websocket]-Verbindung, die für die bidirektionale Kommunikation verwendet wird. Das Protokoll basiert auf https://www.jsonrpc.org/specification[JSON-RPC]. Details zum JSON-RPC finden Sie in der Spezifikation. Als grobe Zusammenfassung wird "
"das Protokoll in"

#. type: Labeled list
#: component-communication/index.adoc:14
#, no-wrap
msgid "JSON-RPC Request"
msgstr "JSON-RPC-Anfrage"

#. type: Plain text
#: component-communication/index.adoc:16
msgid "Identified by a unique ID and method name with specific params. Expects a Response."
msgstr "Identifiziert durch eine eindeutige ID und Methodenname mit bestimmten Parametern. Erwartet eine Antwort."

#. type: Labeled list
#: component-communication/index.adoc:17
#, no-wrap
msgid "JSON-RPC Success Response"
msgstr "JSON-RPC Antwort Erfolgreich"

#. type: Plain text
#: component-communication/index.adoc:19
msgid "Referring to the ID of the Request, providing a result which can be empty or hold specific data."
msgstr "Bezieht sich auf die ID der Anfrage und liefert ein Ergebnis, das leer sein kann oder bestimmte Daten speichert."

#. type: Labeled list
#: component-communication/index.adoc:20
#, no-wrap
msgid "JSON-RPC Error Response"
msgstr "JSON-RPC Fehlermeldung"

#. type: Plain text
#: component-communication/index.adoc:22
msgid "Referring to the ID of the Request, providing error code, message and optionally data."
msgstr "Bezogen auf die ID der Anfrage, Bereitstellung von Fehlercode, Nachricht und optional Daten."

#. type: Labeled list
#: component-communication/index.adoc:23
#, no-wrap
msgid "JSON-RPC Notification"
msgstr "JSON-RPC Benachrichtigung"

#. type: Plain text
#: component-communication/index.adoc:25
msgid "Identified by a unique method name with specific params. Does not expect a Response."
msgstr "Identifiziert durch einen einzigartigen Methodennamen mit bestimmten Parametern. Erwartet keine Reaktion."

#. type: Plain text
#: component-communication/index.adoc:27
msgid "The information on this page is useful to understand the internal communication structure and can help if your plan is to replace one component by a custom implementation, e.g. implementing your own frontend application, or if you plan to extend the provided feature-set."
msgstr "Die Informationen auf dieser Seite sind nützlich, um die interne Kommunikationsstruktur zu verstehen und können helfen, wenn es Ihr Plan ist, eine Komponente durch eine benutzerdefinierte Implementierung zu ersetzen, z. B. die Implementierung Ihrer eigenen Frontend-Anwendung, oder wenn Sie planen, das bereitgestellte Feature-Set zu erweitern."

#. type: Title ==
#: component-communication/index.adoc:28
#, no-wrap
msgid "Authenticate UI to Edge/Backend using token"
msgstr "UI mit dem Token zu Edge/Backend autorisiert"

#. type: Plain text
#: component-communication/index.adoc:31
msgid "On opening of the websocket connection to Edge/Backend, the UI is authenticated using an existing token."
msgstr "Beim Öffnen der Websocket-Verbindung zu Edge/Backend wird die UI mit einem bestehenden Token authentifiziert."

#. type: Target for macro image
#: component-communication/index.adoc:32
#, no-wrap
msgid "authenticateWithSessionId.png"
msgstr "authenticateWithSessionId.png"

#. type: Title ==
#: component-communication/index.adoc:34
#, no-wrap
msgid "Authenticate UI to Edge using password"
msgstr "Mit Passwort an Edge anmelden"

#. type: Target for macro image
#: component-communication/index.adoc:36
#, no-wrap
msgid "authenticateWithPassword.png"
msgstr "authenticateWithPassword.png"

#. type: Title ==
#: component-communication/index.adoc:37
#, no-wrap
msgid "Subscribe to Channels"
msgstr "Abonnieren Sie Channels"

#. type: Plain text
#: component-communication/index.adoc:40
msgid "Allows a Component to subscribe on certain Channel values. The latest Channel values are then periodically sent."
msgstr "Ermöglicht es einer Komponente, sich auf bestimmte Channel-Werte anzumelden. Die neuesten Channel-Werte werden dann regelmäßig gesendet."

#. type: Plain text
#: component-communication/index.adoc:42
msgid "image:subscribeChannels+currentData.png[subscribeChannels+currentData]"
msgstr "image:subscribeChannels+currentData.png[subscribeChannels+currentData]"

#. type: Title ==
#: component-communication/index.adoc:43
#, no-wrap
msgid "Subscribe to System-Log"
msgstr "Abonniere System-Log"

#. type: Plain text
#: component-communication/index.adoc:46
msgid "Sends the log output of Edge to UI via Backend."
msgstr "Sendet die Protokollausgabe von Edge über Backend an UI."

#. type: Plain text
#: component-communication/index.adoc:48
msgid "image:subscribeSystemLog.png[subscribeSystemLog]"
msgstr "image:subscribeSystemLog.png[subscribeSystemLog]"

#. type: Title ==
#: component-communication/index.adoc:50
#, no-wrap
msgid "Store channel data in Time-Series database"
msgstr "Die Kanaldaten in der Datenbank der Zeit-Reihe speichern"

#. type: Plain text
#: component-communication/index.adoc:53
msgid "Edge Backend Api-Controller periodically sends data of Channels to Backend, where it is stored in a Time-Series database (like InfluxDB) via a Timedata service."
msgstr "Edge Backend Api-Controller sendet regelmäßig Daten von Channels an Backend, wo sie über einen Timedata-Dienst in einer Datenbank der Zeitserie (wie InfluxDB) gespeichert werden."

#. mermaid:timestampedData.mmd[]
#. mermaid:edgeConfiguration[]
#. mermaid:authenticate[]
#. mermaid:getStatusOfEdges[]
#. type: Title ==
#: component-communication/index.adoc:63
#, no-wrap
msgid "Communicate with a specific Edge Component"
msgstr "Kommunizieren Sie mit einer bestimmten Edge-Komponente"

#. type: Plain text
#: component-communication/index.adoc:66
msgid "This provides a way to send a JSON-RPC Request directly to a specific Edge Component identified by its Component-ID - e.g. to a specific Controller. To enable this, the Edge Component needs to implement the 'JsonApi' interface."
msgstr "Dies bietet eine Möglichkeit, eine JSON-RPC-Anfrage direkt an eine bestimmte Edge Component zu senden, die durch die Component-ID identifiziert wird-z.B. an einen bestimmten Controller. Um dies zu ermöglichen, muss die Edge-Komponente die ' JsonApi '-Schnittstelle implementieren."

#. mermaid:componentJsonApi[]
#. type: Title =
#: coreconcepts.adoc:1
#, no-wrap
msgid "Core concepts & terminology "
msgstr "Kernkonzepte und Terminologie"

#. type: Plain text
#: coreconcepts.adoc:13
msgid "This chapter describes some of the core concepts and commonly used terms in OpenEMS:"
msgstr "Dieses Kapitel beschreibt einige der Kernkonzepte und in OpenEMS verwendeten Begriffe:"

#. type: Title ==
#: coreconcepts.adoc:14
#, no-wrap
msgid "OSGi Bundle"
msgstr "OSGi Bundle"

#. type: Plain text
#: coreconcepts.adoc:17
msgid "OpenEMS Edge is using the https://en.wikipedia.org/wiki/OSGi[OSGi icon:external-link[]] platform to provide a completely modular and dynamic service oriented system."
msgstr "OpenEMS Edge nutzt die Plattform https://en.wikipedia.org/wiki/OSGi[OSGi icon:external-link[]], um ein komplett modulares und dynamisches, serviceorientiertes System zu bieten."

#. type: Plain text
#: coreconcepts.adoc:19
msgid "Logical groups of source code are put into one OSGi Bundle. Every directory in the source code root directory starting with 'io.openems.*' is a bundle."
msgstr "Logische Gruppen des Quellcodes werden in ein OSGi Bundle gelegt. Jedes Verzeichnis im Quellcode-Root-Verzeichnis, das mit 'io.openems. *' beginnt, ist ein Bündel."

#. type: Title ==
#: coreconcepts.adoc:20
#, no-wrap
msgid "OpenEMS Component"
msgstr "OpenEMS Komponente"

#. type: Plain text
#: coreconcepts.adoc:23
msgid "OpenEMS Edge is built of Components, i.e. every main component implements the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.common/src/io/openems/edge/common/component/OpenemsComponent.java[OpenemsComponent interface icon:code[]]."
msgstr "OpenEMS Edge ist aus Komponenten gebaut, d.h. jede Hauptkomponente implementiert den link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.common/src/io/openems/edge/common/component/OpenemsComponent.java[OpenemsComponent Interface icon:code[]]."

#. type: Plain text
#: coreconcepts.adoc:25
msgid "By definition each Component has a unique ID. Those *Component-IDs* are typically:"
msgstr "Per Definition hat jede Komponente eine eindeutige ID. Diese *Komponenten-IDs* sind typischerweise:"

#. type: Plain text
#: coreconcepts.adoc:27
msgid "`ess0` for the first storage system or battery inverter"
msgstr "`ess0` für das erste Speichersystem oder den Wechselrichter"

#. type: Plain text
#: coreconcepts.adoc:28
msgid "`ess1` for the second storage system or battery inverter"
msgstr "`ess1` für das zweite Speichersystem oder den Wechselrichter"

#. type: Plain text
#: coreconcepts.adoc:29 coreconcepts.adoc:31
msgid "..."
msgstr "..."

#. type: Plain text
#: coreconcepts.adoc:30
msgid "`meter0` for the first meter in the system"
msgstr "`meter0` für den ersten Zähler im System"

#. type: Plain text
#: coreconcepts.adoc:33
msgid "If you receive your OpenEMS together with a FENECON energy storage system, you will find the following Component-IDs:"
msgstr "Wenn Sie Ihr OpenEMS zusammen mit einem FENECON Energiespeicher erhalten, finden Sie folgende Component-IDs:"

#. TODO link:https://github.com/OpenEMS/openems/blob/develop/edge/src/io/openems/impl/device/pro/FeneconProEss.java[FENECON Pro Ess icon:code[]]
#. TODO link:https://github.com/OpenEMS/openems/blob/develop/edge/src/io/openems/impl/device/socomec/SocomecMeter.java[Socomec grid meter icon:code[]]
#. TODO link:https://github.com/OpenEMS/openems/blob/develop/edge/src/io/openems/impl/device/pro/FeneconProPvMeter.java[FENECON Pro production meter icon:code[]]
#. type: Plain text
#: coreconcepts.adoc:41
msgid "FENECON Pro ** `ess0`: FENECON Pro Ess ** `meter0`: Socomec grid meter ** `meter1`: FENECON Pro production meter"
msgstr "FENECON Pro **`ess0`: FENECON Pro Ess** `meter0`: Socomec-Netzzähler **`meter1`: FENECON Pro Erzeugungszähler"

#. TODO link:https://github.com/OpenEMS/openems/blob/develop/edge/src/io/openems/impl/device/minireadonly/FeneconMiniEss.java[FENECON Mini icon:code[]]
#. TODO link:https://github.com/OpenEMS/openems/blob/develop/edge/src/io/openems/impl/device/minireadonly/FeneconMiniGridMeter.java[FENECON Mini grid meter icon:code[]]
#. TODO link:https://github.com/OpenEMS/openems/blob/develop/edge/src/io/openems/impl/device/minireadonly/FeneconMiniProductionMeter.java[FENECON Mini production meter icon:code[]]
#. type: Plain text
#: coreconcepts.adoc:49
msgid "FENECON Mini ** `ess0`: FENECON Mini ** `meter0`: FENECON Mini grid meter ** `meter1`: FENECON Mini production meter"
msgstr "FENECON Mini **`ess0`: FENECON Mini** `meter0`: FENECON Mini Netzzähler **`meter1`: FENECON Mini Erzeugungszähler"

#. type: Title ==
#: coreconcepts.adoc:50
#, no-wrap
msgid "Channel"
msgstr "Channel"

#. type: Plain text
#: coreconcepts.adoc:53
msgid "Each OpenemsComponent provides a number of Channels. Each represents a single piece of information. Each Channel implements the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.common/src/io/openems/edge/common/channel/Channel.java[Channel interface icon:code[]]. By definition each Channel has a unique ID within its parent Component."
msgstr "Jeder OpenemsComponent bietet eine Reihe von Channels. Jeder stellt eine einzige Information dar und implementiert das link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.common/src/io/openems/edge/common/channel/Channel.java[Channel Interface icon:code[]]. Per Definition hat jeder Channel eine eindeutige ID innerhalb seiner Mutterkomponente."

#. == Synchronize device communication
#. == Active/Reactive power control of a battery inverter
#. type: Title =
#: coreconcepts.adoc:54 edge/nature.adoc:1
#, no-wrap
msgid "Nature"
msgstr "Nature"

#. type: Plain text
#: coreconcepts.adoc:57
msgid ""
"Natures extend normal Java interfaces with 'Channels'. If a Component implements a Nature it also needs to provide the required Channels. For example the Energy Storage System (ESS) Simulator link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.simulator/src/io/openems/edge/simulator/ess/symmetric/reacting/EssSymmetric.java[Simulator.EssSymmetric.Reacting icon:code[]] implements the link:https://github.com/"
"OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/api/SymmetricEss.java[Ess interface icon:code[]] and therefor needs to provide a `Soc` Channel that provides the current 'State of Charge' of the battery."
msgstr ""
"Die Natures erweitern normale Java-Schnittstellen mit 'Channels'. Wenn ein Bauteil eine Natur implementiert, muss sie auch die benötigten Kanäle bereitstellen. Zum Beispiel das Energiespeicher-System (ESS) Simulator link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.simulator/src/io/openems/edge/simulator/ess/symmetric/reacting/EssSymmetric.java[Simulator.EssSymmetric.Reacting icon:code[]] implementiert das "
"link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/api/SymmetricEss.java[Ess-Interface icon:code[]] und muss daher einen `Soc`-Channel zur Verfügung stellen, der den aktuellen 'Zustand der Ladung' der Batterie anzeigt."

#. type: Plain text
#: coreconcepts.adoc:59
msgid "xref:edge/controller.adoc[Controllers] are written against Nature implementations. Example: A Controller can be used with any ESS, because it can be sure that it provides all the data the Controller requires for its algorithm."
msgstr "xref:edge/controller.adoc[Controller] verwenden Nature-Implementierungen. Beispiel: Ein Controller kann mit jedem ESS verwendet werden, weil er sicher sein kann, dass dieser alle Daten liefert, die der Controller für seinen Algorithmus benötigt."

#. TODO: add link to all Natures below. Existing Nature implementations are described below.
#. type: Title ==
#: coreconcepts.adoc:62
#, no-wrap
msgid "Channel Address"
msgstr "Channel-Adresse"

#. type: Plain text
#: coreconcepts.adoc:65
msgid "By combining the unique *Component-ID* and *Channel-ID* each Channel in the system can be addressed by a distinct 'Channel Address' in the form `Component-ID/Channel-ID`."
msgstr "Durch die Kombination der eindeutigen *Component-ID* und *Channel-ID* kann jeder Channel im System durch eine eigene 'Channel-Adresse' in der Form 'Component-ID/Channel-ID' angesprochen werden."

#. type: Plain text
#: coreconcepts.adoc:67
msgid "Example: the state of charge (\"Soc\") of the first energy storage system (\"ess0\") has the channel address `ess0/Soc`."
msgstr "Beispiel: Der Ladezustand (\"Soc\") des ersten Energiespeichers (\"ess0\") hat die Kanaladresse `ess0/Soc`."

#. type: Title =
#: coreconcepts.adoc:68 edge/nature.adoc.d/io.openems.edge.scheduler.api.adoc:1 edge/scheduler.adoc:1
#, no-wrap
msgid "Scheduler"
msgstr "Scheduler"

#. type: Plain text
#: coreconcepts.adoc:71
msgid "The Scheduler handles the order, in which Controllers are executed. For details see xref:edge/controller.adoc[Scheduler and Controller] below."
msgstr "Der Scheduler bearbeitet den Auftrag, in dem Controller ausgeführt werden. Weitere Informationen finden Sie unter xref:edge/controller.adoc[Scheduler und Controller] unten."

#. type: Title =
#: coreconcepts.adoc:74 edge/controller.adoc:1 edge/device_service.adoc:1 edge/nature.adoc.d/io.openems.edge.controller.api.adoc:1
#, no-wrap
msgid "Controller"
msgstr "Steuerung"

#. type: Plain text
#: coreconcepts.adoc:76
msgid "The actual business logic or algorithms are wrapped as 'Controllers'. i.e. they implement the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.controller.api/src/io/openems/edge/controller/api/Controller.java[Controller interface icon:code[]]. Each Controller holds one specific, encapsulated task."
msgstr "Die eigentliche Geschäftslogiken oder Algorithmen werden als 'Controller' verpackt. Das heißt, sie implementieren das link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.controller.api/src/io/openems/edge/controller/api/Controller.java[Controller-Interface icon:code[]]. Jeder Controller hat eine spezifische, gekapselte Aufgabe."

#. type: Title =
#: documentation.adoc:1
#, no-wrap
msgid "Documentation"
msgstr "Dokumentation"

#. type: Title ==
#: documentation.adoc:12
#, no-wrap
msgid "Concept"
msgstr "Konzept"

#. type: Plain text
#: documentation.adoc:15
msgid "The OpenEMS Documentation is built with https://antora.org/[Antora icon:external-link[]]."
msgstr "Die OpenEMS-Dokumentation ist mit https:/antora.org/[Antora icon:external-link[]] aufgebaut."

#. type: Plain text
#: documentation.adoc:17
msgid "OpenEMS https://github.com/OpenEMS/openems[Files icon:external-link[]] and https://github.com/OpenEMS/openems.io[Documentation icon:external-link[]] are seperated on two different repositories."
msgstr "OpenEMS https://github.com/OpenEMS/openems [Files icon:external-link[]] und https:/github.com/OpenEMS/OpenEMS/openems.io[Dokumentation icon:external-link[]] werden auf zwei verschiedenen Repositories getrennt."

#. type: Plain text
#: documentation.adoc:19
msgid "The https://github.com/OpenEMS/openems/blob/develop/doc/build/uibundle_openems.zip?raw=true[UI icon:external-link[]] was slightly modified to fit our needs."
msgstr "Die https://github.com/OpenEMS/openems/blob/develop/doc/build/uibundle_openems.zip?raw=true[UI icon:external-link[]] wurde leicht an unsere Bedürfnisse angepasst."

#. type: Plain text
#: documentation.adoc:21
msgid "For more Information regarding https://asciidoctor.org/docs/asciidoc-writers-guide/[AsciiDoc icon:external-link[]] and https://docs.antora.org/antora/1.1/[Antora icon:external-link[]] see their Docs."
msgstr "Für weitere Informationen zu https:/asciidoctor.org/docs/asciidoc-writers-guide/[AsciiDoc icon:external-link[]] und https://docs.antora.org/antora/1.1/[Antora icon:external-link[]] lesen Sie bitte die Dokumentationen der Projekte."

#. type: Title ==
#: documentation.adoc:22
#, no-wrap
msgid "Edit a page"
msgstr "Eine Seite bearbeiten"

#. type: Plain text
#: documentation.adoc:25
msgid "If you want to edit a Docs - Page just click on 'Edit this Page' in the upper right corner to edit the page you are currently visiting."
msgstr "Wenn Sie eine Docs-Seite bearbeiten wollen, klicken Sie einfach auf \"Diese Seite bearbeiten\" in der oberen rechten Ecke, um die Seite zu bearbeiten, die Sie gerade besuchen."

#. type: Plain text
#: documentation.adoc:27
msgid "Edit the Page according to your ideas and commit your changes."
msgstr "Bearbeiten Sie die Seite nach Ihren Vorstellungen und übertragen Sie Ihre Änderungen."

#. type: Plain text
#: documentation.adoc:29
msgid "You will see the changes you have made after our frequent Docs - Update."
msgstr "Sie werden die Änderungen sehen, die Sie nach unserem häufigen Update vorgenommen haben."

#. type: Title ==
#: documentation.adoc:31
#, no-wrap
msgid "Add a page"
msgstr "Eine Seite hinzufügen"

#. type: Plain text
#: documentation.adoc:34
msgid "To add a page, clone the OpenEMS Repository (preferably xref:gettingstarted.adoc#_download_the_source_code[Source Tree]) and open it with a Code Editor (prefably xref:gettingstarted.adoc#_setup_visual_studio_code_for_openems_ui[Visual Studio Code])."
msgstr "Um eine Seite hinzuzufügen, klonen Sie das OpenEMS-Repository (vorzugsweise xref:gettingstarted.adoc#_download_the_source_code[Source Tree]) und öffnen Sie es mit einem Code Editor (vorzugsweise xref:gettingstarted.adoc#_setup_visual_studio_code_for_openems_ui[Visual Studio Code])."

#. type: Plain text
#: documentation.adoc:36
msgid "Go to https://github.com/OpenEMS/openems/tree/develop/doc/modules/ROOT/pages[doc/modules/ROOT/pages icon:external-link[]] and create a .adoc file with the desired name."
msgstr "Gehen Sie zu https:/github.com/OpenEMS/openems/entwicklung/doc/modules/ROOT/pages[doc/modules/ROOT/pages icon:external-link[]] und erstellen Sie eine .adoc-Datei mit dem gewünschten Namen."

#. type: Plain text
#: documentation.adoc:38
msgid "Go to https://github.com/OpenEMS/openems/blob/develop/doc/modules/ROOT/nav.adoc[doc/modules/ROOT/nav.adoc icon:external-link[]] and add your page with the correct filename to the nav file."
msgstr "Gehen Sie zu https:/github.com/OpenEMS/openems/blob/doc/modules/modu.adoc [doc/modules/ROOT/nav.adoc (doc/modules/ROOT/nav.adoc icon:external-link[]] und fügen Sie Ihre Seite mit dem korrekten Dateinamen in die nav-Datei ein."

#. type: Title ==
#: documentation.adoc:41
#, no-wrap
msgid "Build Docs"
msgstr "Docs bauen"

#. type: Plain text
#: documentation.adoc:44
msgid "To build the docs, clone the OpenEMS Repository (preferably xref:gettingstarted.adoc#_download_the_source_code[Source Tree]) and open it with a Code Editor (prefably xref:gettingstarted.adoc#_setup_visual_studio_code_for_openems_ui[Visual Studio Code])."
msgstr "Um die Docs zu bauen, klonen Sie das OpenEMS-Repository (vorzugsweise xref:gettingstarted.adoc#_download_the_source_code[Source Tree]) und öffnen Sie es mit einem Code Editor (vorzugsweise xref:gettingstarted.adoc#_setup_visual_studio_code_for_openems_ui[Visual Studio Code])."

#. type: Plain text
#: documentation.adoc:46
msgid "NodeJS has to be installed"
msgstr "NodeJS muss installiert werden"

#. type: Block title
#: documentation.adoc:47
#, no-wrap
msgid "Install Antora via npm"
msgstr "Antora über npm installieren"

#. type: Plain text
#: documentation.adoc:50
msgid "Open Terminal and type:"
msgstr "Öffnen Sie das Terminal und tippen Sie:"

#. type: delimited block =
#: documentation.adoc:52
msgid "npm i -g @antora/cli"
msgstr "npm i -g @antora/cli"

#. type: delimited block =
#: documentation.adoc:54
msgid "npm i -g @antora/site-generator-default"
msgstr "npm i -g @antora/site-generator-default"

#. type: Block title
#: documentation.adoc:56
#, no-wrap
msgid "Run Antora"
msgstr "run Antora"

#. type: Plain text
#: documentation.adoc:59
msgid "Go to https://github.com/OpenEMS/openems/tree/develop/doc/build[doc/build icon:external-link[]] and type"
msgstr "Gehen Sie zu https://github.com/OpenEMS/openems/tree/develop/doc/build[doc/build icon:external-link[]] und tippen Sie"

#. type: delimited block =
#: documentation.adoc:61
msgid "antora site.yml"
msgstr "antora site.yml"

#. type: Plain text
#: documentation.adoc:63
msgid "Docs should be building.. the finished HTML Folder can be found in your local 'build' folder (inside your https://github.com/OpenEMS/openems/tree/develop/doc/build[repository build folder icon:external-link[]])"
msgstr "Docs sollten gebaut werden .. Der fertige HTML-Ordner kann in Ihrem lokalen \"build\"-Ordner gefunden werden (in Ihrem https:/github.com/OpenEMS/OpenEMS/openems/entwicklung/doc/build[Projektarchiv build-Ordner icon:external-link[]])"

#. type: Plain text
#: documentation.adoc:65
msgid "If your data isn't updating properly or not building, try antora site.yml --pull to reset the cache"
msgstr "Wenn Ihre Daten nicht richtig aktualisieren oder nicht aufgebaut sind, versuchen Sie antora site.yml--pull, um den Cache zurückzusetzen "

#. type: Plain text
#: documentation.adoc:67
msgid "If you want to build your own docs with Antora see https://fabianfnc.github.io/bocs/[this guide icon:external-link[]]"
msgstr "Wenn Sie Ihre eigenen Docs mit Antora bauen wollen, besuchen Sie https://fabianfnc.github.io/bocs/[diesem Leitfaden icon:external-link[]]"

#. type: Title =
#: edge/architecture.adoc:1
#, no-wrap
msgid "Edge Architecture"
msgstr "Edge Architecture"

#. type: Plain text
#: edge/architecture.adoc:14 introduction.adoc:10
msgid "OpenEMS is a modular platform for energy management applications.  It was developed around the requirements of controlling, monitoring and integrating energy storage systems together with renewable energy sources and complementary devices and services."
msgstr "OpenEMS ist eine modulare Plattform für Energiemanagement-Anwendungen.  Es wurde um die Anforderungen der Steuerung, Überwachung und Integration von Energiespeichern zusammen mit erneuerbaren Energiequellen und ergänzenden Geräten und Dienstleistungen entwickelt."

#. type: Plain text
#: edge/architecture.adoc:16
msgid "The OpenEMS Edge software architecture is carefully designed to abstract device communication and control algorithms in a way to provide maximum flexibility, predictability and stability, while simplifying the process of implementing new components."
msgstr "Die Software-Architektur von OpenEMS Edge wurde sorgfältig entwickelt, um die Kommunikation und Steuerung von Algorithmen von Geräten so zu abstrahieren, dass maximale Flexibilität, Vorhersehbarkeit und Stabilität gewährleistet sind, während gleichzeitig der Prozess der Implementierung neuer Komponenten vereinfacht wird."

#. type: Title ==
#: edge/architecture.adoc:17
#, no-wrap
msgid "Input-Process-Output"
msgstr "Eingabe-Verarbeitung-Ausgabe"

#. type: Plain text
#: edge/architecture.adoc:20
msgid "OpenEMS Edge is built around the well-known IPO (input-process-output) model which defines the internal execution cycle."
msgstr "OpenEMS Edge ist um das bekannte IPO-Modell (Input-Process-Output, \"Eingabe-Verarbeitung-Ausgabe\") aufgebaut, das den internen Ausführungszyklus definiert."

#. type: Block title
#: edge/architecture.adoc:21
#, no-wrap
msgid "Input-Process-Output model"
msgstr "Eingabe-Verarbeitung-Ausgabe-Modell"

#. type: Target for macro image
#: edge/architecture.adoc:22
#, no-wrap
msgid "input-process-output.png"
msgstr "input-process-output.png"

#. type: Labeled list
#: edge/architecture.adoc:24
#, no-wrap
msgid "Input"
msgstr "Eingabe"

#. type: Plain text
#: edge/architecture.adoc:26
msgid "During the input phase all relevant information - e.g. the current 'state of charge' of a battery - is collected and provided as a *process image*. This process image is guaranteed to never change during the cycle."
msgstr "Während der Eingangsphase werden alle relevanten Informationen - z. B. der aktuelle Ladezustand einer Batterie-gesammelt und als *Prozessabbild* zur Verfügung gestellt. Dieses Prozessabbild wird sich während des Zyklus garantiert nie ändern."

#. type: Labeled list
#: edge/architecture.adoc:27
#, no-wrap
msgid "Process"
msgstr "Prozess"

#. type: Plain text
#: edge/architecture.adoc:29
msgid "The process phase runs algorithms and tasks based on the process image - e.g. an algorithm uses the 'state of charge' information to evaluate whether a digital output should be turned on."
msgstr "In der Prozessphase werden Algorithmen und Aufgaben auf Basis des Prozessbildes ausgeführt-z.B. nutzt ein Algorithmus die \"State of Charge\"-Informationen, um zu bewerten, ob eine digitale Ausgabe eingeschaltet werden soll."

#. type: Labeled list
#: edge/architecture.adoc:30
#, no-wrap
msgid "Output"
msgstr "Ausgabe"

#. type: Plain text
#: edge/architecture.adoc:32
msgid "The output phase takes the results from the process phase and applies it - e.g. it turns the digital output on or off."
msgstr "Die Ausgabephase nimmt die Ergebnisse aus der Prozessphase auf und wendet sie an - z. B. schaltet sie die digitale Ausgabe ein oder aus."

#. type: Title ==
#: edge/architecture.adoc:33
#, no-wrap
msgid "Scheduler and Controller"
msgstr "Planer und Controller"

#. type: Plain text
#: edge/architecture.adoc:36
msgid "During the 'process' phase different algorithms (Controllers) might try to access the same resources - e.g. two Controllers try to switch the same digital output. It is therefor necessary to prioritize their execution and restrict access according to priority."
msgstr "Während der 'process'-Phase können verschiedene Algorithmen (Controller) versuchen, auf die gleichen Ressourcen zuzugreifen-z.B. zwei Controller versuchen, die gleiche digitale Ausgabe zu schalten. Daher ist es notwendig, ihre Ausführung zu priorisieren und den Zugang nach Priorität zu beschränken."

#. type: Plain text
#: edge/architecture.adoc:38
msgid "OpenEMS Edge uses Scheduler implementations to receive a sorted list of Controllers. The Controllers are then executed in order. Later executed Controllers are not allowed to overwrite a previously written result."
msgstr "OpenEMS Edge verwendet Scheduler-Implementierungen, um eine sortierte Liste von Controllern zu erhalten. Die Controller werden dann in Ordnung ausgeführt. Spätere ausgeführte Controller dürfen ein zuvor geschriebenes Ergebnis nicht überschreiben."

#. type: Block title
#: edge/architecture.adoc:39
#, no-wrap
msgid "IPO model with Scheduler and Controllers"
msgstr "IPO-Modell mit Scheduler und Controllern"

#. type: Target for macro image
#: edge/architecture.adoc:40
#, no-wrap
msgid "input-process-scheduler-output.png"
msgstr "input-process-scheduler-output.png"

#. type: Title ==
#: edge/architecture.adoc:42
#, no-wrap
msgid "Cycle"
msgstr "Zyklus"

#. type: Plain text
#: edge/architecture.adoc:45
msgid ""
"The input-process-output model in OpenEMS Edge is executed in a Cycle - implemented by the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.core/src/io/openems/edge/core/cycle/Cycle.java[Cycle component icon:code[]]. It handles the setting of a process image in the input phase and executes the Controllers in the process phase. Furthermore it emits Cycle Events that can be used in other Components to "
"synchronize with the Cycle."
msgstr ""
"Das Input-Prozess-Output-Modell in OpenEMS Edge wird in einem Zyklus ausgeführt-implementiert durch den link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.core/src/io/openems/edge/core/cycle/Cycle.java[Cycle-Komponenten icon:code[]]. Es übernimmt die Einstellung eines Prozessbildes in der Eingangsphase und führt die Controller in der Prozessphase aus. Darüber hinaus gibt es Cycle Events aus, die in anderen "
"Komponenten verwendet werden können, um sich mit dem Zyklus zu synchronisieren."

#. type: Block title
#: edge/architecture.adoc:46
#, no-wrap
msgid "OpenEMS Edge Cycle"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte"

#. type: Target for macro image
#: edge/architecture.adoc:47
#, no-wrap
msgid "edge-cycle.png"
msgstr "edge-cycle.png"

#. type: Title ==
#: edge/architecture.adoc:49
#, no-wrap
msgid "Asynchronous threads and Cycle synchronization"
msgstr "Asynchrone Threads und Zyklus-Synchronisation"

#. type: Plain text
#: edge/architecture.adoc:52
msgid "Communication with external hardware and services needs to be executed in asynchronous threads to not block the system. At the same time, those threads need to synchronize with the Cycle."
msgstr "Die Kommunikation mit externen Hardware und Diensten muss in asynchronen Threads erfolgen, um das System nicht zu blockieren. Gleichzeitig müssen diese Fäden mit dem Zyklus synchronisiert werden."

#. type: Plain text
#: edge/architecture.adoc:54
msgid "The following example shows, how the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.bridge.modbus/src/io/openems/edge/bridge/modbus/AbstractModbusBridge.java[Modbus implementation icon:code[]] uses Cycle Events to synchronize with the Cycle:"
msgstr "Das folgende Beispiel zeigt, wie der link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.bridge.modbus/src/io/openems/edge/bridge/modbus/AbstractModbusBridge.java[Modbus Implementation icon:code[]] Cycle Events verwendet, um Synchronisation mit dem Zyklus:"

#. type: Block title
#: edge/architecture.adoc:55
#, no-wrap
msgid "Synchronize Cycle with Modbus read/write "
msgstr "Zyklusarbeit mit Modbus lesend/schreiben"

#. type: Target for macro image
#: edge/architecture.adoc:56
#, no-wrap
msgid "cycle-modbus.png"
msgstr "cycle-modbus.png"

#. type: Title ==
#: edge/architecture.adoc:58
#, no-wrap
msgid "Architecture scheme"
msgstr "Architekturschema"

#. type: Plain text
#: edge/architecture.adoc:61
msgid "The following scheme shows the abstraction of hardware via Channels, Natures and Devices as well as the execution of control algorithms via Scheduler and Controllers."
msgstr "Das folgende Schema zeigt die Abstraktion von Hardware über Kanäle, Naturen und Geräte sowie die Ausführung von Steuerungsalgorithmen über Scheduler und Controllers."

#. type: Block title
#: edge/architecture.adoc:62
#, no-wrap
msgid "Architecture scheme "
msgstr "Architekturschema"

#. type: Target for macro image
#: edge/architecture.adoc:63
#, no-wrap
msgid "device-nature-channel-scheduler-controller.png"
msgstr "device-nature-channel-scheduler-controller.png"

#. type: Title =
#: edge/bridge.adoc:1
#, no-wrap
msgid "Bridge"
msgstr "Bridge"

#. type: Plain text
#: edge/bridge.adoc:13
msgid "To simplify the implementation of hardware that is connected via certain standardized physical connection layers and protocols, those are implemented as Bridges."
msgstr "Um die Implementierung von Hardware zu vereinfachen, die über bestimmte standardisierte physikalische Verbindungsebenen und Protokolle verbunden ist, werden diese als Brücken implementiert."

#. TODO
#. === Developing a Bridge
#. type: Title =
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:1
#, no-wrap
msgid "Modbus"
msgstr "Modbus"

#. type: Plain text
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:4
msgid "Modbus is a widely used standard for fieldbus connections. It is used by all kinds of hardware devices like photovoltaics inverters, electric meters, and so on."
msgstr "Modbus ist ein weit verbreiteter Standard für Feldbusverbindungen. Es wird von allen Arten von Hardware-Geräten wie Photovoltaik-Wechselrichter, elektrische Zähler, und so weiter verwendet."

#. type: Title ==
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:5
#, no-wrap
msgid "Modbus/TCP"
msgstr "Modbus/TCP"

#. type: Labeled list
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:7
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.bridge.modbus/src/io/openems/edge/bridge/modbus/BridgeModbusTcpImpl.java[Modbus/TCP icon:code[]]"
msgstr "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.bridge.modbus/src/io/openems/edge/bridge/modbus/BridgeModbusTcpImpl.java[Modbus/TCP icon:code[]]"

#. TODO add configuration settings
#. type: Plain text
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:10
msgid "https://en.wikipedia.org/wiki/Modbus[Modbus/TCP icon:external-link[]] for fieldbus connections via TCP/IP network."
msgstr "https://en.wikipedia.org/wiki/Modbus [Modbus/TCP icon:external-link[]] für Feldbusverbindungen über das TCP/IP-Netzwerk."

#. type: Title ==
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:11
#, no-wrap
msgid "Modbus/RTU"
msgstr "Modbus/RTU"

#. type: Labeled list
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:13
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.bridge.modbus/src/io/openems/edge/bridge/modbus/BridgeModbusSerialImpl.java[Modbus/Serial icon:code[]]"
msgstr "link: https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.bridge.modbus/src/io/openems/edge/bridge/modbus/BridgeModbusSerialImpl.java [Modbus/Serial icon:code[]]"

#. TODO add configuration settings
#. type: Plain text
#: edge/bridge.adoc.d/io.openems.edge.bridge.modbus.adoc:15
msgid "https://en.wikipedia.org/wiki/Modbus[Modbus/RTU icon:external-link[]] for fieldbus connections via RS485 serial bus."
msgstr "https://en.wikipedia.org/wiki/Modbus [Modbus/RTU icon:external-link[] für Feldbusverbindungen über den seriellen Bus RS485."

#. type: Title =
#: edge/build.adoc:1
#, no-wrap
msgid "Build OpenEMS Edge"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte"

#. type: Plain text
#: edge/build.adoc:17
msgid "Inside Eclipse IDE open the *io.openems.edge.application* project and open the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.application/EdgeApp.bndrun[EdgeApp.bndrun icon:code[]] file."
msgstr "Inside Eclipse IDE open the * io.openems.edge.application * project and open the link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.application/EdgeApp.bndrun[EdgeApp.bndrun icon:code[]] file."

#. type: Block title
#: edge/build.adoc:18 edge/implement.adoc:382
#, no-wrap
msgid "Eclipse IDE EdgeApp.bndrun"
msgstr "Eclipse IDE EdgeApp.bndrun"

#. type: Target for macro image
#: edge/build.adoc:19 edge/implement.adoc:383
#, no-wrap
msgid "eclipse-edgeapp-bndrun.png"
msgstr "eclipse-edgeapp-bndrun.png"

#. type: Block title
#: edge/build.adoc:25
#, no-wrap
msgid "Eclipse Export Wizard Selection assistant"
msgstr "Eclipse Export Wizard Selection Assistent"

#. type: Target for macro image
#: edge/build.adoc:26
#, no-wrap
msgid "eclipse-bnd-file-export.png"
msgstr "eclipse-bnd-file-export.png"

#. type: Block title
#: edge/build.adoc:30
#, no-wrap
msgid "Eclipse Export Destination"
msgstr "Eclipse Export-Destination"

#. type: Target for macro image
#: edge/build.adoc:31
#, no-wrap
msgid "eclipse-bnd-file-export-destination.png"
msgstr "eclipse-bnd-file-export-destination.png"

#. type: Plain text
#: edge/build.adoc:36
msgid "This creates a so called Fat-JAR-file including all bundles. It can be executed by running `java -jar openems.jar` in a console."
msgstr "Dadurch wird eine sogenannte Fat-JAR-Datei erstellt, die alle Bundles enthält. Es kann ausgeführt werden, indem man ' java-jar openems.jar ' in einer Konsole läuft."

#. type: Plain text
#: edge/build.adoc:40
msgid "link:https://gradle.org/[Gradle] is a build tool that is used in the OpenEMS project to compile the JAR-files and to execute other tasks like building the documentation webpage using link:https://antora.org/[Antora] and the Javadocs. To build OpenEMS Edge:"
msgstr "link: https:/gradle.org/[Gradle] ist ein Build-Tool, das im OpenEMS-Projekt verwendet wird, um die JAR-Dateien zu kompilieren und andere Aufgaben auszuführen, wie zum Beispiel das Bauen der Dokumentations-Webseite mit link:https:/antora.org/[Antora] und die Javadocs. OpenEMS Edge zu bauen:"

#. type: Plain text
#: edge/build.adoc:43
msgid "Execute `gradlew buildEdge`"
msgstr "Ausführung ' gradlew buildEdge '"

#. type: Plain text
#: edge/configuration.adoc:13
msgid "OpenEMS Edge and Backend are configured using the standard OSGi configuration admin service. The easiest way to set a configuration is via the http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]] as described in the xref:gettingstarted.adoc[Getting Started] guide above."
msgstr "OpenEMS Edge und Backend werden mit dem Standard-OSGi-Konfigurationsadmin-Service konfiguriert. Der einfachste Weg, eine Konfiguration zu setzen, ist über das http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]], wie oben im xref:gettingstarted.adoc[Getting Started]-Leitfaden beschrieben."

#. type: Block title
#: edge/configuration.adoc:14 gettingstarted.adoc:84
#, no-wrap
msgid "Apache Felix Web Console Configuration"
msgstr "Apache Felix Web-Konsolen-Konfiguration"

#. type: Target for macro image
#: edge/configuration.adoc:15 gettingstarted.adoc:85
#, no-wrap
msgid "apache-felix-console-configuration.png"
msgstr "apache-felix-console-configuration.png"

#. type: Plain text
#: edge/configuration.adoc:18
msgid "Configuration via OpenEMS UI is currently not available due to the ongoing migration to xref:coreconcepts.adoc#_osgi_bundle[OSGi]. Once migration is finished, it is going to be possible to change every configuration using the settings menu in OpenEMS UI - directly to OpenEMS Edge and via Backend."
msgstr "Die Konfiguration über OpenEMS UI ist derzeit aufgrund der fortschreitenden Migration zu xref: coreconcepts.adoc # _ osgi _ bundle [OSGi] nicht verfügbar. Sobald die Migration beendet ist, wird es möglich sein, jede Konfiguration über das Einstellungsmenü in OpenEMS UI zu ändern-direkt an OpenEMS Edge und über Backend."

#. type: Block title
#: edge/configuration.adoc:19
#, no-wrap
msgid "OpenEMS UI Configuration"
msgstr "OpenEMS UI Screenshots"

#. type: Target for macro image
#: edge/configuration.adoc:20
#, no-wrap
msgid "ui-config.png"
msgstr "ui-config.png"

#. type: Plain text
#: edge/controller.adoc:13
msgid "A OpenEMS Edge Controller holds the actual business logic or the actual algorithm that controls hardware. The logic of each active Controller is executed regularly on every Cycle, i.e. once per second."
msgstr "Ein OpenEMS Edge Controller hält die eigentliche Geschäftslogik oder den eigentlichen Algorithmus, der Hardware steuert. Die Logik jedes aktiven Controllers wird regelmäßig auf jedem Zyklus ausgeführt, also einmal pro Sekunde."

#. TODO
#. === Developing a Controller
#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.api.backend.adoc:1
#, no-wrap
msgid "Api Backend"
msgstr "Api Backend"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.backend.adoc:4
msgid "Connects to OpenEMS Backend and sends all Channel data regularly. It is implemented as a Controller, as Channels can be written from OpenEMS Backend."
msgstr "Verbindet sich mit OpenEMS Backend und sendet regelmäßig alle Channel-Daten. Es wird als Controller implementiert, da Channels von OpenEMS Backend geschrieben werden können."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.backend.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.backend[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.backend [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.api.modbus.adoc:1
#, no-wrap
msgid "Api Modbus"
msgstr "Api Modbus"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.modbus.adoc:4
msgid "Provides a Modbus-Slave implementation for OpenEMS Edge. It provides access to Channels from an external device via Modbus/TCP."
msgstr "Bietet eine Modbus-Slave-Implementierung für OpenEMS Edge. Es ermöglicht den Zugriff auf Kanäle von einem externen Gerät über Modbus/TCP."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.modbus.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.modbus[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.modbus [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:1
#, no-wrap
msgid "REST-Api Controller"
msgstr "REST-Api Controller"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:4
msgid "A REST-Api for external access to OpenEMS Edge. This Controller provides access to Channels and JSON-RPC Requests from an external device via JSON/REST."
msgstr "Ein REST-Api für den externen Zugriff auf OpenEMS Edge. Dieser Controller ermöglicht den Zugriff auf Kanäle und JSON-RPC-Anfragen von einem externen Gerät über JSON/REST."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:6
msgid "The default port for the server is *8084*; so the default base address for REST calls is `http://x:<PASSWORD>@<IP>:8084/rest`, where"
msgstr "Der Standard-Port für den Server ist * 8084 *; So lautet die Standard-Basisadresse für REST-Anrufe ' http:/x: <PASSWORD> @ <IP>: 8084/rest ', wo"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:8
msgid "`http` is the protocol"
msgstr "' http ' ist das Protokoll"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:9
msgid "`x` is the user. Authentication in OpenEMS is via password only, so the username can be omitted."
msgstr "\"x\" ist der Benutzer. Die Authentifizierung in OpenEMS erfolgt nur über ein Passwort, so dass der Benutzername weggelassen werden kann."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:10
msgid "`<PASSWORD>` is the user password. If no specific settings have been made, try 'user' or 'admin' here."
msgstr "\"<PASSWORD>\" ist das Passwort des Benutzers. Wenn keine bestimmten Einstellungen vorgenommen wurden, versuchen Sie hier ' Benutzer ' oder ' Admin '."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:11
msgid "`8084` is the configured port"
msgstr "' 8084 ' ist der konfigurierte Port"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:13
msgid "A good way to test REST-Api calls is via the Chrome extension https://chrome.google.com/webstore/detail/restlet-client-rest-api-t/aejoelaoggembcahagimdiliamlcdmfm[Restlet]"
msgstr "Eine gute Möglichkeit, REST-Api-Anrufe zu testen, ist über die Chrome-Erweiterung https://chrome.google.com/webstore/detail/restlet-client-rest-api-t/aejoelaoggembcahagimdiliamlcdmfm[Restlet]"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:15
msgid "For more information find the implementation https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.rest[Source Code icon:github[]]."
msgstr "Weitere Informationen finden Sie unter https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.rest [Source Code Symbol: github []]."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:17
msgid "Those are the available REST-Api endpoints:"
msgstr "Das sind die verfügbaren REST-Api-Endpunkte:"

#. type: Title ==
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:18
#, no-wrap
msgid "Endpoint `/rest/channel/<Component-ID>/<Channel-ID>`"
msgstr "Endpunkt '/rest/channel/<Component-ID>/<Channel-ID> '"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:21
msgid "`Component-ID` is the ID of the Component, e.g. \"_sum\", \"ess0\", \"meter0\",..."
msgstr "' Component-ID ' ist die ID der Komponente, z.B. \"_ sum\", \"ess0\", \"meter0\",..."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:22
msgid "`Channel-ID` is the ID of the Channel, e.g. \"ActivePowerL1\", \"Soc\",..."
msgstr "' Channel-ID ' ist die ID des Kanals, z.B. \"ActivePowerL1\", \"Soc\",..."

#. type: Title ===
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:23
#, no-wrap
msgid "GET"
msgstr "GET"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:26
msgid "Use a HTTP request with method `GET` to read the current value of a Channel."
msgstr "Verwenden Sie eine HTTP-Anfrage mit der Methode ' GET ', um den aktuellen Wert eines Kanals zu lesen."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:33
#, no-wrap
msgid ""
"*Example:* To read the current state of charge of the battery, send a GET request to ****************************/rest/_sum/EssSoC. It returns a response like:\n"
"```\n"
"{\n"
"  \"value\": 50\n"
"}\n"
"``` \n"
msgstr ""
"* Beispiel: * Um den aktuellen Ladezustand der Batterie zu lesen, senden Sie eine GET-Anfrage an ****************************/rest/_sum/EssSoC. Es gibt eine Antwort wie:\n"
"```\n"
"{\n"
"  \"Wert\": 50\n"
"}\n"
"```\n"

#. type: Title ===
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:34
#, no-wrap
msgid "POST"
msgstr "POST"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:37
msgid "Use a HTTP request with method `POST` to write a Channel."
msgstr "Verwenden Sie eine HTTP-Anfrage mit der Methode ' POST ', um einen Kanal zu schreiben."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:44
#, no-wrap
msgid ""
"*Example:* To switch a Digital-Output or Relay on, send a POST request to ****************************/rest/io0/Relay1 with the following body:\n"
"```\n"
"{\n"
"  \"value\": true\n"
"}\n"
"```\n"
msgstr ""
"* Beispiel: * Um eine Digital-Ausgabe oder Relay einzuschalten, senden Sie eine POST-Anfrage an ****************************/rest/io0/Relay1 mit folgendem Körper:\n"
"```\n"
"{\n"
"  \"Wert\": Stimmt\n"
"}\n"
"```\n"

#. type: Title ==
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:45
#, no-wrap
msgid "Endpoint '/jsonrpc'"
msgstr "Endpunkt '/jsonrpc '"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:48
msgid "This allows remote procedure calls (RPC) using JSON-RPC. The JSON-RPC commands need to be sent as `POST` requests with the specified body."
msgstr "Dies ermöglicht Fernabläufe (RPC) mit JSON-RPC. Die JSON-RPC-Befehle müssen als ' POST '-Anfragen mit dem angegebenen Körper gesendet werden."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:50
msgid "JSON-RPC usually requires the properties 'id' and 'jsonrpc'. Those can be omitted here, as they are not required for HTTP POST calls."
msgstr "JSON-RPC benötigt in der Regel die Eigenschaften ' ID ' und ' jsonrpc '. Diese können hier weggelassen werden, da sie bei HTTP-POST-Anrufen nicht benötigt werden."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:52
msgid "Following JSON-RPC commands are available:"
msgstr "Folgende JSON-RPC-Befehle sind verfügbar:"

#. type: Title ===
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:53
#, no-wrap
msgid "getEdgeConfig"
msgstr "GetEdgeConfig"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:56
msgid "Gets the current configuration of the OpenEMS Edge."
msgstr "Erhält die aktuelle Konfiguration des OpenEMS Edge."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:63
#, no-wrap
msgid ""
"```\n"
"{\n"
"  \"method\": \"getEdgeConfig\",\n"
"  \"params\": {}\n"
"}\n"
"```\n"
msgstr ""
"```\n"
"{\n"
"  \"Methode\": \"GetEdgeConfig\",\n"
"  \"Params\": {{}\n"
"}\n"
"```\n"

#. type: Title ===
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:64
#, no-wrap
msgid "componentJsonApi"
msgstr "EntjsonApi"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:67
msgid "Forwards a JSON-RPC payload to a given Component, identified by its Component-ID."
msgstr "Schenkt eine JSON-RPC-Nutzlast an eine bestimmte Komponente weiter, die durch die Component-ID identifiziert wird."

#. type: Title ====
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:68
#, no-wrap
msgid "getModbusProtocol"
msgstr "getModbusProtocol"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:71
msgid "Gets the currently active Modbus-TCP protocol definition from the Modbus-TCP-Api Controller with the ID \"ctrlModbusTcp0\"."
msgstr "Erhält die aktuell aktive Modbus-TCP-Protokolldefinition aus dem Modbus-TCP-Api-Controller mit der ID \"ctrlModbusTcp0\"."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:80
#, no-wrap
msgid ""
"```\n"
"{\n"
"  \"method\":\"componentJsonApi\",\n"
"  \"params\":{\n"
"    \"componentId\":\"ctrlApiModbusTcp0\",\n"
"    \"payload\":{\n"
"      \"method\":\"getModbusProtocol\",\n"
"      \"params\":{\n"
msgstr ""
"```\n"
"{\n"
"  \"Methode\": \"KomponentJsonApi\",\n"
"  \"Params\": {\n"
"    \"componentId\":\"ctrlApiModbusTcp0\",\n"
"    \"Nutzlast\": {\n"
"      \"Methode\": \"getModbusProtocol\",\n"
"      \"Params\": {\n"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:86
#, no-wrap
msgid ""
"      }\n"
"    }\n"
"  }\n"
"}\n"
"```\n"
msgstr ""
"}\n"
"    }\n"
"  }\n"
"}\n"
"```\n"

#. type: Title ===
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:87
#, no-wrap
msgid "updateComponentConfig"
msgstr "UpdateComponentConfig"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:90
msgid "Updates a Component configuration."
msgstr "Aktualisiert eine Komponentenkonfiguration."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.rest.adoc:102
#, no-wrap
msgid ""
"```\n"
"{\n"
"\t\"method\": \"updateComponentConfig\",\n"
"\t\"params\": {\n"
"\t\t\"componentId\": \"ctrlDebugLog0\",\n"
"\t\t\"properties\": [{\n"
" \t\t\t\"name\": \"enabled\",\n"
"\t\t\t\"value\": true\n"
"\t\t}]\n"
"\t}\n"
"}\n"
"```\n"
msgstr ""
"```\n"
"{\n"
"\t\"Methode\": \"updateComponentConfig\",\n"
"\t\"Params\": {\n"
"\t\t\"ComponentId\": \"ctrlDebugLog0\",\n"
"\t\t\"Eigenschaften\": [[\n"
" \t\t\t\"Name\": \"Aktiviert\",\n"
"\t\t\t\"Wert\": Stimmt\n"
"\t\t}]\n"
"\t}\n"
"}\n"
"```\n"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.api.websocket.adoc:1
#, no-wrap
msgid "Api Websocket"
msgstr "Api Websocket"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.websocket.adoc:4
msgid "Provides a JSON/REST implementation via HTTP Websocket for OpenEMS Edge. It provides access to Channels and JSON-RPC Requests from an external device via Websocket. This Controller is used for local connection of OpenEMS UI."
msgstr "Bietet eine JSON/REST-Implementierung über HTTP-Websocket für OpenEMS Edge. Es ermöglicht den Zugriff auf Kanäle und JSON-RPC-Anfragen von einem externen Gerät über Websocket. Dieser Controller wird für die lokale Verbindung von OpenEMS UI verwendet."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.api.websocket.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.websocket[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.api.websocket [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.balancingcosphi.adoc:1
#, no-wrap
msgid "Asymmetric Balancing Cos-Phi"
msgstr "Asymmetrischer Balancing Cos-Phi"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.balancingcosphi.adoc:4
msgid "Controls an asymmetric energy storage system in self-consumption optimization mode while keeping the grid meter on a defined cos-phi."
msgstr "Steuert ein asymmetrisches Energiespeicher-System im Selbstverbrauchsoptimierungsmodus, während der Gittermesser auf einem definierten Cos-Phi gehalten wird."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.balancingcosphi.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.balancingcosphi[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.balancingcosphi [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.fixactivepower.adoc:1
#, no-wrap
msgid "Asymmetric Fix Active Power"
msgstr "Asymmetrische Fix Aktive Kraft"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.fixactivepower.adoc:4
msgid "Sets a fixed active power for charging/discharging of an asymmetric energy storage system."
msgstr "Setzt eine feste aktive Leistung für die Entsorgung eines asymmetrischen Energiespeichers."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.fixactivepower.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.fixactivepower[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.fixactivepower [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.fixreactivepower.adoc:1
#, no-wrap
msgid "Asymmetric Fix Reactive Power"
msgstr "Asymmetrische Fix Reaktive Kraft"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.fixreactivepower.adoc:4
msgid "Sets a fixed reactive power for an asymmetric energy storage system."
msgstr "Setzt eine feste Reaktionsleistung für einen asymmetrischen Energiespeicher."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.fixreactivepower.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.fixreactivepower[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.fixreactivepower [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.phaserectification.adoc:1
#, no-wrap
msgid "Asymmetric Phase Rectification"
msgstr "Asymmetrische Phasenrezertifizierung"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.phaserectification.adoc:4
msgid "Balances the three phases at the grid using an asymmetric energy storage system."
msgstr "Bilden Sie die drei Phasen des Netzes mit einem asymmetrischen Energiespeicher."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.asymmetric.phaserectification.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.phaserectification[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.asymmetric.phaserectification [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.channelthreshold.adoc:1
#, no-wrap
msgid "Channel-Threshold"
msgstr "Kanaldrohold"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.channelthreshold.adoc:4
msgid "Generic Controller that sets a digital output according to the value of given Channel - e.g. turn a Relay on, when battery state of charge is above a given threshold."
msgstr "Generischer Controller, der eine digitale Ausgabe nach dem Wert des gegebenen Kanals setzt-z.B. ein Relay einschalten, wenn der Batteriezustand über einer bestimmten Schwelle liegt."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.channelthreshold.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.channelthreshold[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.channelthreshold [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.chp.soc.adoc:1
#, no-wrap
msgid "CHP control via State-of-Charge (SoC)"
msgstr "BHK-Steuerung über State-of-Charge (SoC)"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.chp.soc.adoc:4
msgid "Controls a CHP device. Signals the CHP to turn on when battery SoC is low; signal it to turn off when SoC is high."
msgstr "Steuert ein KWK-Gerät. Signalisiert, dass die KWK eingeschaltet werden soll, wenn die Batterie SoC niedrig ist; Signalisieren Sie es, um auszuschalten, wenn SoC hoch ist."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.chp.soc.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.chp.soc[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.chp.soc [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.debug.detailedlog.adoc:1
#, no-wrap
msgid "Detailed Debug Log"
msgstr "Detailliertes Debug-Log"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.debug.detailedlog.adoc:4
msgid "Constantly shows the values of all Channels of a Component on the console. Primarily used for developing and debugging."
msgstr "Zeigt ständig die Werte aller Kanäle eines Bauteils auf der Konsole an. Vor allem für die Entwicklung und Fehlersuche."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.debug.detailedlog.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.debug.detailedlog[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.debug.detailedlog [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.debug.log.adoc:1
#, no-wrap
msgid "Debug Log"
msgstr "Debug Log (Fehlerprotokoll)"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.debug.log.adoc:4
msgid "Constantly shows the most important values of all Components on the console. This is often activated by default to be able to track the running system easily."
msgstr "Zeigt ständig die wichtigsten Werte aller Komponenten auf der Konsole. Dies wird oft standardmäßig aktiviert, um das laufende System leicht verfolgen zu können."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.debug.log.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.debug.log[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.debug.log [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.dischargelimitconsideringcellvoltage.adoc:1
#, no-wrap
msgid "ESS Discharge Limit Considering Cell Voltage"
msgstr "ESS-Diskostenlimit bei Cell Voltage"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.dischargelimitconsideringcellvoltage.adoc:4
msgid "Limit discharge of an energy storage system depending on cell voltages."
msgstr "Grenzentladung eines Energiespeichers je nach Zellspannungen."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.dischargelimitconsideringcellvoltage.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.dischargelimitconsideringcellvoltage[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.dischargelimitconsideringcellvoltage [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.ess.acisland.adoc:1
#, no-wrap
msgid "ESS AC-Island"
msgstr "ESS AC-Island"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.ess.acisland.adoc:4
msgid "Switches an AC PV inverter to the emergency power on grid outage."
msgstr "Schaltet einen AC-PV-Wechselrichter bei Netzausfall in die Notstromversorgung."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.ess.acisland.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.ess.acisland[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.ess.acisland [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.ess.limittotaldischarge.adoc:1
#, no-wrap
msgid "ESS Limit Total Discharge"
msgstr "ESS-Limit Total abgabe"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.ess.limittotaldischarge.adoc:4
msgid "Limits the discharge power of an energy storage system according to its State-of-Charge, e.g. to keep energy for emergency power or to avoid deep discharge."
msgstr "Limitiert die Entladungsleistung eines Energiespeichers nach seinem Ladezustand, z.B. um Energie für Notstromversorgung zu halten oder um eine tiefe Entladung zu vermeiden."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.ess.limittotaldischarge.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.ess.limittotaldischarge[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.ess.limittotaldischarge [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.ess.onefullcycle.adoc:1
#, no-wrap
msgid "ESS One Full Cycle"
msgstr "ESS Ein vollwertiges Rad"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.ess.onefullcycle.adoc:4
msgid "Executes a full charge/discharge cycle with an energy storage system. This can be used to let the Battery Management System (BMS) reset its reference points for State-of-Charge calculattion."
msgstr "Führt einen kompletten Ladezyklus mit einem Energiespeicher durch. Damit kann das Batteriemanagementsystem (BMS) seine Bezugspunkte für die Berechnung der Ladezustände zurücksetzen."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.ess.onefullcycle.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.ess.onefullcycle[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.ess.onefullcycle [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.evcs.adoc:1
#, no-wrap
msgid "EVCS"
msgstr "EVCS"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.evcs.adoc:4
msgid "Controls an Electric Vehicle Charging Station (EVCS) in different modes, like \"Force-Charge\" and \"Surplus Energy Charging\"."
msgstr "Steuert eine Elektrofahrzeug-Ladestation (EVCS) in verschiedenen Modi, wie \"Force-Charge\" und \"Surplus Energy Charging\"."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.evcs.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.evcs[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.evcs [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.evcs.fixactivepower.adoc:1
#, no-wrap
msgid "EVCS Fix Active Power"
msgstr "EVCS Fix Active Power"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.evcs.fixactivepower.adoc:4
msgid "Sets a fixed maximum charge power to an Electric Vehicle Charging Station (EVCS)."
msgstr "Setzt eine feste maximale Ladeleistung an eine elektrische Fahrzeugladestation (EVCS)."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.evcs.fixactivepower.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.evcs.fixactivepower[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.evcs.fixactivepower [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.highloadtimeslot.adoc:1
#, no-wrap
msgid "High-Load Timeslot"
msgstr "Hochladenzeitplan"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.highloadtimeslot.adoc:4
msgid "Controls an energy storage system for a High-Load timeslot application (German \"Hochlastzeitfenster\")."
msgstr "Steuert ein Energiespeicher-System für eine High-Load-Zeitlauf-Anwendung (Hochlastzeitfenster)."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.highloadtimeslot.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.highloadtimeslot[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.highloadtimeslot [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.io.alarm.adoc:1
#, no-wrap
msgid "IO Alarm"
msgstr "IO Alarm"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.io.alarm.adoc:4
msgid "Switches a digital output, when one or more State-Channels are set. Can be used to signal alarms."
msgstr "Schaltet eine digitale Ausgabe, wenn ein oder mehrere State-Kanäle gesetzt sind. Kann verwendet werden, um Alarme zu signalisieren."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.io.alarm.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.io.alarm[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.io.alarm [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.io.fixdigitaloutput.adoc:1
#, no-wrap
msgid "IO Fix Digital Output"
msgstr "IO Fix Digital Output"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.io.fixdigitaloutput.adoc:4
msgid "Sets a digital output statically ON or OFF."
msgstr "Setzt einen digitalen Ausgang statisch ein oder aus."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.io.fixdigitaloutput.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.io.fixdigitaloutput[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.io.fixdigitaloutput [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.pvinverter.fixpowerlimit.adoc:1
#, no-wrap
msgid "PV-Inverter Fix Power Limit"
msgstr "PV-Wechselrichter fixieren Power Limit"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.pvinverter.fixpowerlimit.adoc:4
msgid "Sets a fixed power limit for PV-Inverter production."
msgstr "Setzt ein festes Leistungslimit für die PV-Wechselriter-Produktion."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.pvinverter.fixpowerlimit.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.pvinverter.fixpowerlimit[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.pvinverter.fixpowerlimit [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.balancing.adoc:1
#, no-wrap
msgid "Symmetric Balancing"
msgstr "Symmetrisches Balancing"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.balancing.adoc:4
msgid "Controls a symmetric energy storage system in self-consumption optimization mode."
msgstr "Steuert ein symmetrisches Energiespeicher im Eigenverbrauchsoptimierungsmodus."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.balancing.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.balancing[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.balancing [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.balancingschedule.adoc:1
#, no-wrap
msgid "Symmetric Balancing Schedule"
msgstr "Symmetrisches Balancing-Programm"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.balancingschedule.adoc:4
msgid "Controls a symmetric energy storage system in self-consumption optimization mode. Allows the definition of a Schedule to set the target power on the grid meter. This Controller can be controlled using the OpenEMS Backend-to-Backend interface."
msgstr "Steuert ein symmetrisches Energiespeicher im Eigenverbrauchsoptimierungsmodus. Ermöglicht die Definition eines Zeitplans, um die Zielleistung auf dem Netzmeter einzustellen. Dieser Controller kann über die OpenEMS Backend-to-Backend-Schnittstelle gesteuert werden."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.balancingschedule.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.balancingschedule[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.balancingschedule [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.fixactivepower.adoc:1
#, no-wrap
msgid "Symmetric Fix Active Power"
msgstr "Symmetrische Fix Aktive Kraft"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.fixactivepower.adoc:4
msgid "Sets a fixed active power for charging/discharging of a symmetric energy storage system."
msgstr "Setzt eine feste aktive Leistung für die Ladung eines symmetrischen Energiespeichers."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.fixactivepower.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.fixactivepower[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.fixactivepower [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.fixreactivepower.adoc:1
#, no-wrap
msgid "Symmetric Fix Reactive Power"
msgstr "Symmetrische Fix Reaktive Kraft"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.fixreactivepower.adoc:4
msgid "Sets a fixed reactive power for a symmetric energy storage system."
msgstr "Setzt eine feste Reaktionsleistung für ein symmetrisches Energiespeicher."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.fixreactivepower.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.fixreactivepower[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.fixreactivepower [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.limitactivepower.adoc:1
#, no-wrap
msgid "Symmetric Limit Active Power"
msgstr "Symmetrische Limit Aktive Kraft"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.limitactivepower.adoc:4
msgid "Limits the allowed active power for charging and discharging of a symmetric energy storage system."
msgstr "Limits die zulässige aktive Leistung zum Aufladen und Entladen eines symmetrischen Energiespeichers."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.limitactivepower.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.limitactivepower[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.limitactivepower [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.linearpowerband.adoc:1
#, no-wrap
msgid "Symmetric Linear Power Band"
msgstr "Symmetrische Linearkraftband"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.linearpowerband.adoc:4
msgid "Executes a test cycle for a symmetric energy storage system by increasing and decreasing the charging/discharging power in given limits."
msgstr "Führt einen Testzyklus für ein symmetrisches Energiespeicher-System durch, indem die Ladekraft in vorgegebenen Grenzen erhöht und verringert wird."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.linearpowerband.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.linearpowerband[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.linearpowerband [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.peakshaving.adoc:1
#, no-wrap
msgid "Symmetric Peak-Shaving"
msgstr "Symmetrischer Peak-Shaving"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.peakshaving.adoc:4
msgid "Applies peak-shaving at the grid using a symmetric energy storage system."
msgstr "Mit Hilfe eines symmetrischen Energiespeichers wird die Peak-Rasitur am Gitter eingesetzt."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.peakshaving.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.peakshaving[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.peakshaving [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.randompower.adoc:1
#, no-wrap
msgid "Symmetric Random-Power"
msgstr "Symmetrische Random-Power"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.randompower.adoc:4
msgid "Applies random charging/discharging of a symmetric energy storage system for performance tests."
msgstr "Gilt die zufällige Befladung eines symmetrischen Energiespeichers für Leistungstests."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.randompower.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.randompower[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.randompower [Source Code icon: github []]"

#. type: Title =
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.reactivepowervoltagecharacteristic.adoc:1
#, no-wrap
msgid "Symmetric Reactive-Power Voltage-Characteristics"
msgstr "Symmetrische Reaktions-und Kraft-Voltage-Charakteristik"

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.reactivepowervoltagecharacteristic.adoc:4
msgid "Controls a symmetric energy storage system using a Q-by-U reference function."
msgstr "Steuert ein symmetrisches Energiespeicher-System mit einer Q-by-U-Referenzfunktion."

#. type: Plain text
#: edge/controller.adoc.d/io.openems.edge.controller.symmetric.reactivepowervoltagecharacteristic.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.reactivepowervoltagecharacteristic[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.controller.symmetric.reactivepowervoltagecharacteristic [Source Code icon: github []]"

#. type: Plain text
#: edge/deploy.adoc:13
msgid "This chapter explains how OpenEMS can be deployed on a Debian Linux Internet-of-Things Gateway. Similar techniques will work for other operating systems as well."
msgstr "In diesem Kapitel wird erklärt, wie OpenEMS auf einem Debian Linux Internet-of-Things Gateway eingesetzt werden kann. Ähnliche Techniken werden auch für andere Betriebssysteme funktionieren."

#. type: Plain text
#: edge/deploy.adoc:15
msgid "This guide covers a simple, manual approach. For productive systems it is required to automate deployment to IoT devices. Good approaches include a Debian package repository that provides *.deb-files and third-party tools like http://www.eclipse.org/hawkbit/[Eclipse Hawkbit]. This is out-of-scope for this small guide."
msgstr "Diese Anleitung umfasst einen einfachen, manuellen Ansatz. Für produktive Systeme ist es erforderlich, den Einsatz auf IoT-Geräten zu automatisieren. Gute Ansätze sind ein Debian-Paket-Projektarchiv, das * .deb-Dateien und Drittanbieter-Tools wie http://www.eclipse.org/hawkbit/[Eclipse Hawkbit] zur Verfügung stellt. Das ist für diesen kleinen Leitfaden nicht möglich."

#. type: Plain text
#: edge/deploy.adoc:17
msgid "Prerequisites:"
msgstr "Voraussetzungen:"

#. type: Plain text
#: edge/deploy.adoc:19
msgid "A target device running Debian Linux like a Raspberry Pi, Beaglebone Black or an IoT gateway. You need the IP address and SSH access."
msgstr "Ein Zielgerät, das Debian Linux wie ein Raspberry Pi, Beaglebone Black oder ein IoT-Gateway betreibt. Sie benötigen die IP-Adresse und den SSH-Zugang."

#. type: Plain text
#: edge/deploy.adoc:20
msgid "Create a JAR-file that should be deployes. See xref:edge/build.adoc[Build OpenEMS Edge] for details."
msgstr "Erstellen Sie eine JAR-Datei, die eingestellt werden soll. Weitere Informationen finden Sie unter xref: edge/build.adoc [Build OpenEMS Edge]."

#. type: Plain text
#: edge/deploy.adoc:21
msgid "Setup an SSH client to connect to the Linux console, e.g. http://www.9bis.net/kitty/[KiTTY]"
msgstr "Richten Sie einen SSH-Client ein, um sich mit der Linux-Konsole zu verbinden, z.B. http://www.9bis.net/kitty/[KiTTY]"

#. type: Plain text
#: edge/deploy.adoc:22
msgid "Setup an SCP client to copy the JAR file via SSH, e.g. https://winscp.net/eng/docs/lang:de[WinSCP]"
msgstr "Setup eines SCP-Clients, um die JAR-Datei über SSH zu kopieren, z.B. https:/winscp.net/docs/lang: de [WinSCP]"

#. type: Title ==
#: edge/deploy.adoc:23
#, no-wrap
msgid "Connect via SSH and SCP"
msgstr "Verbinden Sie sich über SSH und SCP"

#. type: Plain text
#: edge/deploy.adoc:26
msgid "Connect via SSH using KiTTY"
msgstr "Verbinden Sie sich über SSH mit KiTTY"

#. type: Plain text
#: edge/deploy.adoc:27
msgid "Open KiTTY and connect to the target device."
msgstr "Öffnen Sie KiTTY und verbinden Sie sich mit dem Zielgerät."

#. type: Plain text
#: edge/deploy.adoc:29
msgid "Make sure to select SSH with port 22 and enter the IP address of the target device. Press the btn:[Open] button."
msgstr "Stellen Sie sicher, dass Sie SSH mit Port 22 auswählen und die IP-Adresse des Zielgerätes eingeben. Drücken Sie den btn: [Öffnen] Knopf."

#. type: Block title
#: edge/deploy.adoc:30 edge/deploy.adoc:35
#, no-wrap
msgid "KiTTY Configuration"
msgstr "KiTTY-Konfiguration"

#. type: Target for macro image
#: edge/deploy.adoc:31
#, no-wrap
msgid "deploy-kitty.png"
msgstr "deploy-kitty.png"

#. type: Plain text
#: edge/deploy.adoc:34
msgid "Gain root permissions either by logging in as user *root* or by login in as a default user and executing *sudo -s*."
msgstr "Gewinnen Sie Root-Berechtigungen entweder, indem Sie sich als Benutzer * root * einloggen oder indem Sie sich als Standard-Benutzer anmelden und * sudo-s * ausführen."

#. type: Target for macro image
#: edge/deploy.adoc:36
#, no-wrap
msgid "deploy-ssh-root.png"
msgstr "deploy-ssh-root.png"

#. type: Plain text
#: edge/deploy.adoc:45
msgid "As an example, for the FENECON Energy Management System (FEMS) it is required to: + _login as:_ `fems` + _fems@**************'s password:_ `device specific password` + ... + _fems@femsXXX:~$_ `sudo -s` + _[sudo] password for fems:_ `device specific password` + _root@femsXXX:/home/<USER>"
msgstr "Für das FENECON Energy Management System (FEMS) ist es beispielsweise erforderlich: + _ login as: _ ' fems ' + _fems@**************'s password: _ ' device specific password ' + ... + _ fems @ femsXXX: ~ $ _ ' sudo-s ' + _ [sudo] password for fems: _ ' device specific password ' + root @ femsXXX:/home/<USER>"

#. type: Plain text
#: edge/deploy.adoc:47
msgid "Connect via SCP using WinSCP"
msgstr "Verbinden Sie sich über SCP mit WinSCP"

#. type: Plain text
#: edge/deploy.adoc:49
msgid "If you are lucky and have a fully configured system, right-click on the KiTTY window bar and select btn:[Start WinSCP]."
msgstr "Wenn Sie Glück haben und ein voll konfiguriertes System haben, klicken Sie mit der rechten Maustaste auf die KiTTY-Fensterleiste und wählen Sie btn: [Starten Sie WinSCP]."

#. type: Block title
#: edge/deploy.adoc:50 edge/deploy.adoc:55
#, no-wrap
msgid "Start WinSCP from KiTTY"
msgstr "WinSCP von KiTTY starten"

#. type: Target for macro image
#: edge/deploy.adoc:51
#, no-wrap
msgid "deploy-kitty-start-winscp.png"
msgstr "deploy-kitty-start-winscp.png"

#. type: Plain text
#: edge/deploy.adoc:54
msgid "Otherwise open WinSCP separately, create a new connection, select *SCP* as protocol and enter again the IP address and port 22. Click btn:[Connect] once finished."
msgstr "Ansonsten öffnen Sie WinSCP separat, erstellen Sie eine neue Verbindung, wählen Sie * SCP * als Protokoll und geben Sie die IP-Adresse und den Port 22 wieder ein. Klicken Sie auf btn: [Connect], sobald sie fertig sind."

#. type: Target for macro image
#: edge/deploy.adoc:56
#, no-wrap
msgid "deploy-winscp.png"
msgstr "deploy-winscp.png"

#. type: Plain text
#: edge/deploy.adoc:63
msgid "Create the directory */usr/lib/openems*. This is going to be the place, where we put the JAR file."
msgstr "Erstellen Sie das Verzeichnis */usr/lib/openems *. Dies wird der Ort sein, an dem wir die JAR-Datei stellen."

#. type: Plain text
#: edge/deploy.adoc:65
msgid "Execute `mkdir /usr/lib/openems`."
msgstr "Führt ' mkdir/usr/lib/openems ' aus."

#. type: Plain text
#: edge/deploy.adoc:69
msgid "Create the directory */etc/openems.d*. This is going to be the place, where all the bundle configurations are held."
msgstr "Erstellen Sie das Verzeichnis */etc/openems.d *. Dies wird der Ort sein, an dem alle Bündelkonfigurationen stattfinden."

#. type: Plain text
#: edge/deploy.adoc:71
msgid "Execute `mkdir /etc/openems.d`."
msgstr "Ausführbar ' mkdir/etc/openems.d '."

#. type: Plain text
#: edge/deploy.adoc:75
msgid "The systemd 'Service Manager' manages system processes in a Debian Linux. We will create a systemd service definition file, so that systemd takes care of managing (starting/restarting/...) the OpenEMS Edge service."
msgstr "Der Systemd ' Service Manager ' verwaltet Systemprozesse in einem Debian Linux. Wir werden eine Systemd-Service-Definition-Datei erstellen, so dass sich systemd um die Verwaltung (starting/restarting/...) des OpenEMS Edge Service kümmert."

#. type: Plain text
#: edge/deploy.adoc:79
msgid "Execute `nano /etc/systemd/system/openems.service`"
msgstr "Ausführbar ' nano/etc/systemd/systemd/openems.service '"

#. type: delimited block -
#: edge/deploy.adoc:102
#, no-wrap
msgid ""
"[Service]\n"
"User=root <3>\n"
"Group=root\n"
"Type=notify <4>\n"
"WorkingDirectory=/usr/lib/openems\n"
"LimitCORE=infinity\n"
"LimitRTPRIO=2\n"
"LimitRTTIME=60000000\n"
"CPUSchedulingPolicy=rr\n"
"CPUSchedulingPriority=1\n"
"ExecStart=/usr/bin/java -Dfelix.cm.dir=/etc/openems.d/ -jar /usr/lib/openems/openems.jar <5>\n"
"SuccessExitStatus=143 <6>\n"
"Restart=always <7>\n"
"RestartSec=10 <8>\n"
"WatchdogSec=60 <9>\n"
msgstr ""
"[Service]\n"
"User = root <3>\n"
"Gruppe = Wurzel\n"
"Type = benachrichtigen <4>\n"
"Working-Verzeichnis =/usr/lib/openems\n"
"LimitCORE = Unendlichkeit\n"
"LimitRTPRIO=2\n"
"LimitRTTIME=60000000\n"
"CPUSchedulingPolicy = rr\n"
"CPUSchedulingPriority=1\n"
"Es ist nicht so, dass es sich um ein-Dfelix.cm.dir um ein sehr schönes und sehr schönes Hotel handelt <5>\n"
"Erfolgs-ExitStatus=143 <6>\n"
"Neustart = immer <7>\n"
"RestartSec=10 <8>\n"
"WatchdogSec=60 <9>\n"

#. type: Plain text
#: edge/deploy.adoc:110
msgid "OpenEMS notifies systemd once it is started up properly."
msgstr "OpenEMS meldet systemd, sobald es richtig gestartet ist."

#. type: Plain text
#: edge/deploy.adoc:111
msgid "The start command. It uses Java 8, sets the config directory to `/etc/openems.d` and runs the jar file at `/usr/lib/openems/openems.jar`"
msgstr "Der Startbefehl. Es verwendet Java 8, setzt das Konfigurationsverzeichnis auf '/etc/openems.d ' und führt die jar-Datei unter '/usr/lib/openems/openems.jar ' aus."

#. type: Plain text
#: edge/deploy.adoc:113
msgid "Systemd _always_ tries to restart OpenEMS once it was quit."
msgstr "Systemd _ always _ tries to restart OpenEMS once that once it be quitit."

#. type: Plain text
#: edge/deploy.adoc:115
msgid "Systemd expects OpenEMS to trigger its watchdog once every 60 seconds. OpenEMS is doing that by default if it detects that it is run by systemd."
msgstr "Systemd erwartet, dass OpenEMS seinen Watchdog einmal alle 60 Sekunden auslöst. OpenEMS tut das standardmäßig, wenn es feststellt, dass es von systemd ausgeführt wird."

#. type: Title ====
#: edge/deploy.adoc:122
#, no-wrap
msgid "Update OpenEMS Edge JAR file"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte"

#. type: Plain text
#: edge/deploy.adoc:125
msgid "To update the OpenEMS JAR file at the target device, it is required to copy the JAR file from your build directory (see xref:edge/build.adoc[Build OpenEMS Edge]) to `/usr/lib/openems/openems.jar` on the target device. Afterwards it is required to restart the systemd service"
msgstr "Um die OpenEMS-JAR-Datei auf dem Zielgerät zu aktualisieren, ist es erforderlich, die JAR-Datei aus Ihrem Build-Verzeichnis zu kopieren (siehe xref: edge/build.adoc [Build OpenEMS Edge]) auf '/usr/lib/openems/openems/openems.jar ' auf dem Zielgerät. Danach ist es erforderlich, den systemd-Dienst neu zu starten"

#. type: Plain text
#: edge/deploy.adoc:127
msgid "Copy JAR file via SCP."
msgstr "Kopieren Sie JAR-Datei über SCP."

#. type: Plain text
#: edge/deploy.adoc:129
msgid "In WinSCP open your local build directory on the left side and */usr/lib/openems/* on the right side. Then drag and drop the file from left to right."
msgstr "In WinSCP öffnen Sie Ihr lokales Build-Verzeichnis auf der linken Seite und */usr/lib/openems/* auf der rechten Seite. Dann ziehen und lassen Sie die Datei von links nach rechts."

#. type: Block title
#: edge/deploy.adoc:130
#, no-wrap
msgid "WinSCP copy file"
msgstr "WinSCP-Kopierdatei"

#. type: Target for macro image
#: edge/deploy.adoc:131
#, no-wrap
msgid "deploy-winscp-copy.png"
msgstr "deploy-winscp-copy.png"

#. type: Plain text
#: edge/deploy.adoc:134
msgid "Restart OpenEMS systemd service."
msgstr "Starten Sie den OpenEMS-Systemd-Service neu."

#. type: Plain text
#: edge/deploy.adoc:136
msgid "Execute `systemctl restart openems --no-block; journalctl -lfu openems`"
msgstr "Führt ' systemctl restart openems--no-block; Journalctl-lfu-openems '"

#. type: Plain text
#: edge/deploy.adoc:138
msgid "The command restarts the service (_systemctl restart openems_) while not waiting for the OpenEMS startup notification (_--no-block_). Then it directly prints the OpenEMS system log (_journalctl -lfu openems_)."
msgstr "Der Befehl startet den Dienst neu (_ systemctl starten openems _), während er nicht auf die OpenEMS-Start-Benachrichtigung (_--no-block _) wartet. Dann druckt es direkt das OpenEMS-Systemprotokoll (_ journalctl-lfu openems _)."

#. type: Block title
#: edge/deploy.adoc:139
#, no-wrap
msgid "OpenEMS Edge start-up"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte"

#. type: Target for macro image
#: edge/deploy.adoc:140
#, no-wrap
msgid "deploy-openems-start.png"
msgstr "deploy-openems-start.png"

#. type: Plain text
#: edge/device_service.adoc:14
msgid "For the following devices driver implementations are available in OpenEMS Edge."
msgstr "Für die folgenden Geräte sind Treiber-Implementierungen in OpenEMS Edge verfügbar."

#. type: Title ==
#: edge/device_service.adoc:17
#, no-wrap
msgid "KACO blueplanet hybrid 10.0 TL3"
msgstr "KACO blueplanet hybrid 10.0 TL3"

#. type: Labeled list
#: edge/device_service.adoc:19 edge/device_service.adoc.d/io.openems.edge.battery.soltaro.adoc:3 edge/device_service.adoc.d/io.openems.edge.ess.byd.container.adoc:3 edge/device_service.adoc.d/io.openems.edge.ess.fenecon.commercial40.adoc:3 edge/device_service.adoc.d/io.openems.edge.ess.kaco.blueplanet.gridsave50.adoc:3 edge/device_service.adoc.d/io.openems.edge.ess.mr.gridcon.adoc:3
#: edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:5 edge/device_service.adoc.d/io.openems.edge.ess.sinexcel.adoc:3 edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:3 edge/device_service.adoc.d/io.openems.edge.evcs.keba.kecontact.adoc:3 edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:5 edge/device_service.adoc.d/io.openems.edge.fenecon.mini.adoc:3
#: edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:3 edge/device_service.adoc.d/io.openems.edge.io.kmtronic.adoc:3 edge/device_service.adoc.d/io.openems.edge.io.wago.adoc:3 edge/device_service.adoc.d/io.openems.edge.kostal.piko.adoc:3 edge/device_service.adoc.d/io.openems.edge.meter.artemes.am2.adoc:3 edge/device_service.adoc.d/io.openems.edge.meter.bcontrol.em300.adoc:3
#: edge/device_service.adoc.d/io.openems.edge.meter.carlo.gavazzi.em300.adoc:3 edge/device_service.adoc.d/io.openems.edge.meter.janitza.umg96rme.adoc:3 edge/device_service.adoc.d/io.openems.edge.meter.microcare.sdm630.adoc:5 edge/device_service.adoc.d/io.openems.edge.meter.socomec.adoc:5 edge/device_service.adoc.d/io.openems.edge.meter.virtual.adoc:3 edge/device_service.adoc.d/io.openems.edge.meter.weidmueller.adoc:3
#: edge/device_service.adoc.d/io.openems.edge.pvinverter.solarlog.adoc:3
#, no-wrap
msgid "Implemented Natures"
msgstr "Implementation Natures"

#. type: Plain text
#: edge/device_service.adoc:21 edge/device_service.adoc.d/io.openems.edge.ess.byd.container.adoc:5 edge/device_service.adoc.d/io.openems.edge.ess.fenecon.commercial40.adoc:5 edge/device_service.adoc.d/io.openems.edge.ess.kaco.blueplanet.gridsave50.adoc:5 edge/device_service.adoc.d/io.openems.edge.ess.mr.gridcon.adoc:5 edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:7
#: edge/device_service.adoc.d/io.openems.edge.ess.sinexcel.adoc:5 edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:5 edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:7 edge/device_service.adoc.d/io.openems.edge.fenecon.mini.adoc:7 edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:5 edge/device_service.adoc.d/io.openems.edge.kostal.piko.adoc:5
msgid "SymmetricEss"
msgstr "SymmetrikEss"

#. type: Plain text
#: edge/device_service.adoc:22 edge/device_service.adoc.d/io.openems.edge.ess.byd.container.adoc:6 edge/device_service.adoc.d/io.openems.edge.ess.fenecon.commercial40.adoc:6 edge/device_service.adoc.d/io.openems.edge.ess.kaco.blueplanet.gridsave50.adoc:6 edge/device_service.adoc.d/io.openems.edge.ess.mr.gridcon.adoc:6 edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:8
#: edge/device_service.adoc.d/io.openems.edge.ess.sinexcel.adoc:6 edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:6 edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:6
msgid "ManagedSymmetricEss"
msgstr "ManagedSymmetricEss"

#. type: Plain text
#: edge/device_service.adoc:23
msgid "(proprietary)"
msgstr "(Eigene)"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.battery.soltaro.adoc:1
#, no-wrap
msgid "Soltaro Battery Rack"
msgstr "Soltaro Batterie-Rack"

#. TODO
#. === Developing a Nature
#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.battery.soltaro.adoc:5 edge/nature.adoc.d/io.openems.edge.battery.api.adoc:1
#, no-wrap
msgid "Battery"
msgstr "Batterie"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.battery.soltaro.adoc:6
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.battery.soltaro[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.battery.soltaro [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.byd.container.adoc:1
#, no-wrap
msgid "FENECON BYD Container"
msgstr "FENECON BYD Container"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.byd.container.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.byd.container[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.byd.container [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.cluster.adoc:1
#, no-wrap
msgid "ESS Cluster"
msgstr "ESS-Cluster"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.cluster.adoc:4
msgid "Combines multiple energy storage systems (ESS) to one common ESS. This way every Controller can easily work with multiple ESS in parallel. Distribution of power requests to each ESS is controlled via the https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.core/src/io/openems/edge/ess/core/power[Power-Class icon:github[]]."
msgstr "Kombiniert mehrere Energiespeicher (ESS) zu einer gemeinsamen ESS. So kann jeder Controller einfach mit mehreren ESS parallel arbeiten. Die Verteilung der Stromanfragen an jeden ESS wird über das https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.core/src/io/openems/edge/ess/core/power [Power-Klasse-Symbol: github []] gesteuert."

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.cluster.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.cluster[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.cluster [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.fenecon.commercial40.adoc:1
#, no-wrap
msgid "FENECON Commercial 40 AC/DC/Hybrid"
msgstr "FENECON Commercial 40 AC/DC/Hybrid"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.fenecon.commercial40.adoc:7 edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:9
msgid "EssDcCharger"
msgstr "EssDcCharger"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.fenecon.commercial40.adoc:8
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.fenecon.commercial40[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.fenecon.commercial40[Source Code Icon: github []"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.kaco.blueplanet.gridsave50.adoc:1
#, no-wrap
msgid "KACO blueplanet gridsave 50.0 TL3"
msgstr "KACO blueplanet gridsave 50.0 TL3"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.kaco.blueplanet.gridsave50.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.kaco.blueplanet.gridsave50[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.kaco.blueplanet.gridsave50[Source Code Icon: github []"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.mr.gridcon.adoc:1
#, no-wrap
msgid "Maschinenfabrik Rheinhausen (MR) Gridcon"
msgstr "Maschinenfabrik Rheinhausen (MR) Gridcon"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.mr.gridcon.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.mr.gridcon[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.mr.gridcon [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:1
#, no-wrap
msgid "REFU Battery Inverter"
msgstr "REFU Batterie-Wechselrichter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:4
msgid "Project specific implementation of a REFU inverter. This will not directly apply to all REFU battery inverters."
msgstr "Projektspezifische Umsetzung eines REFU-Wechselrichters. Dies gilt nicht direkt für alle REFU-Batterie-Wechselrichter."

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:9 edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:7 edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:8 edge/device_service.adoc.d/io.openems.edge.fenecon.mini.adoc:6 edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:7
msgid "AsymmetricEss"
msgstr "AsymmetricEss"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:10 edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:8 edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:8
msgid "ManagedAsymmetricEss"
msgstr "ManagedAsymmetricEss"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.refu.adoc:11
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.refu[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.refu [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.sinexcel.adoc:1
#, no-wrap
msgid "Sinexcel Battery Inverter"
msgstr "Sinexcel Batterie-Wechselrichter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.sinexcel.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.sinexcel[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.sinexcel [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:1
#, no-wrap
msgid "SMA SunnyIsland 6.0H"
msgstr "SMA SunnyIsland 6.0H"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:9 edge/device_service.adoc.d/io.openems.edge.fenecon.mini.adoc:5
msgid "SinglePhaseEss"
msgstr "SinglePhaseEss"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:10
msgid "ManagedSinglePhaseEss"
msgstr "ManagedSinglePhaseEss"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.ess.sma.adoc:11
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.sma[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.ess.sma [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.evcs.keba.kecontact.adoc:1
#, no-wrap
msgid "KEBA KeContact c-series Charging Station"
msgstr "KEBA KeContact C-Serie Ladestation"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.evcs.keba.kecontact.adoc:5
msgid "Evcs (Electric Vehicle Charging Station)"
msgstr "Evcs (Elektrofahrzeug-Ladestation)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.evcs.keba.kecontact.adoc:6
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.evcs.keba.kecontact[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.evcs.keba.kecontact [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:1
#, no-wrap
msgid "FENECON DESS"
msgstr "FENECON DESS"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:4
msgid "Applies to multiple similar products like the FENECON by BYD PRO Hybrid."
msgstr "Gilt für mehrere ähnliche Produkte wie die FENECON von BYD PRO Hybrid."

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:10
msgid "AsymmetricMeter (for Grid and AC-connected PV)"
msgstr "AsymmetricMeter (für Gitter-und AC-angeschlossener PV)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:11
msgid "SymmetricMeter (for Grid and AC-connected PV)"
msgstr "SymmetricMeter (für Gitter und AC-angeschlossener PV)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.dess.adoc:12
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.fenecon.dess[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.fenecon.dess [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.fenecon.mini.adoc:1
#, no-wrap
msgid "FENECON Mini 3-3 | 3-6"
msgstr "FENECON Mini 3-3 | 3-6"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.mini.adoc:8
msgid "SymmetricMeter (for Grid and PV)"
msgstr "SymmetricMeter (für Gitter und PV)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.mini.adoc:9
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.fenecon.mini[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.fenecon.mini [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:1
#, no-wrap
msgid "FENECON Pro 9-12"
msgstr "FENECON Pro 9-12"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:9
msgid "AsymmetricMeter (for PV)"
msgstr "AsymmetricMeter (für PV)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:10
msgid "SymmetricMeter (for PV)"
msgstr "SymmetricMeter (für PV)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.fenecon.pro.adoc:11
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.fenecon.pro[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.fenecon.pro [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.io.kmtronic.adoc:1
#, no-wrap
msgid "KMtronic Modbus Relay Board"
msgstr "KMtronic Modbus Relay Board"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.io.kmtronic.adoc:5 edge/device_service.adoc.d/io.openems.edge.io.wago.adoc:5
msgid "DigitalOutput"
msgstr "DigitalOutput"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.io.kmtronic.adoc:6
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.io.kmtronic[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.io.kmtronic [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.io.wago.adoc:1
#, no-wrap
msgid "WAGO Fieldbus Coupler 750-352"
msgstr "WAGO Fieldbus Coupler 750-352"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.io.wago.adoc:6
msgid "DigitalInput"
msgstr "DigitalInput"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.io.wago.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.io.wago[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.io.wago [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.kostal.piko.adoc:1
#, no-wrap
msgid "KOSTAL PIKO"
msgstr "KOSTAL PIKO"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.kostal.piko.adoc:6
msgid "SymmetricMeter (for Grid meter)"
msgstr "SymmetricMeter (für Gittermesser)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.kostal.piko.adoc:7
msgid "EssDcCharger (for PV)"
msgstr "EssDcCharger (für PV)"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.kostal.piko.adoc:8
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.kostal.piko[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.kostal.piko [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.meter.artemes.am2.adoc:1
#, no-wrap
msgid "Artemes AM-2"
msgstr "Artemes AM-2"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.artemes.am2.adoc:5 edge/device_service.adoc.d/io.openems.edge.meter.bcontrol.em300.adoc:5 edge/device_service.adoc.d/io.openems.edge.meter.carlo.gavazzi.em300.adoc:5 edge/device_service.adoc.d/io.openems.edge.meter.janitza.umg96rme.adoc:5 edge/device_service.adoc.d/io.openems.edge.meter.microcare.sdm630.adoc:7 edge/device_service.adoc.d/io.openems.edge.meter.socomec.adoc:7
#: edge/device_service.adoc.d/io.openems.edge.meter.virtual.adoc:5 edge/device_service.adoc.d/io.openems.edge.meter.weidmueller.adoc:5 edge/device_service.adoc.d/io.openems.edge.pvinverter.solarlog.adoc:6
msgid "SymmetricMeter"
msgstr "SymmetricMeter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.artemes.am2.adoc:6 edge/device_service.adoc.d/io.openems.edge.meter.bcontrol.em300.adoc:6 edge/device_service.adoc.d/io.openems.edge.meter.carlo.gavazzi.em300.adoc:6 edge/device_service.adoc.d/io.openems.edge.meter.janitza.umg96rme.adoc:6 edge/device_service.adoc.d/io.openems.edge.meter.microcare.sdm630.adoc:8 edge/device_service.adoc.d/io.openems.edge.meter.socomec.adoc:8
#: edge/device_service.adoc.d/io.openems.edge.meter.weidmueller.adoc:6
msgid "AsymmetricMeter"
msgstr "AsymmetricMeter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.artemes.am2.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.artemes.am2[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.artemes.am2[Source Code Icon: github []"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.meter.bcontrol.em300.adoc:1
#, no-wrap
msgid "SOCOMEC Diris A10 | A14 | B30 | E24 Meter"
msgstr "SOCOMEC Diris A10 | A14 | B30 | E24 Meter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.bcontrol.em300.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.socomec[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.socomec [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.meter.carlo.gavazzi.em300.adoc:1
#, no-wrap
msgid "Carlo Gavazzi EM300 Meter"
msgstr "Carlo Gavazzi EM300 Meter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.carlo.gavazzi.em300.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.carlo.gavazzi.em300[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.carlo.gavazzi.em300[Source Code Icon: github []"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.meter.janitza.umg96rme.adoc:1
#, no-wrap
msgid "Janitza UMG 96RM-E Meter"
msgstr "Janitza UMG 96RM-E Meter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.janitza.umg96rme.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.janitza.umg96rme[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.janitza.umg96rme[Source Code Icon: github []"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.meter.microcare.sdm630.adoc:1 edge/device_service.adoc.d/io.openems.edge.meter.socomec.adoc:1
#, no-wrap
msgid "Microcare SDM 630 Meter"
msgstr "Mikrocare SDM 630 Meter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.microcare.sdm630.adoc:4 edge/device_service.adoc.d/io.openems.edge.meter.socomec.adoc:4
msgid "This implementation is functionally compatible with a number of energy meters with the name \"SDM 630\"."
msgstr "Diese Implementierung ist funktionell kompatibel mit einer Reihe von Energiezählern mit dem Namen \"SDM 630\"."

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.microcare.sdm630.adoc:9 edge/device_service.adoc.d/io.openems.edge.meter.socomec.adoc:9
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.microcare.sdm630[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.microcare.sdm630[Source Code Icon: github []"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.meter.virtual.adoc:1
#, no-wrap
msgid "Virtual Symmetric Add"
msgstr "Virtuelle Symmetrie"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.virtual.adoc:6
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.virtual[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.virtual [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.meter.weidmueller.adoc:1
#, no-wrap
msgid "Weidmueller 525 Meter"
msgstr "Weidmueller 525 Meter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.meter.weidmueller.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.weidmueller[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.meter.weidmueller [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.pvinverter.solarlog.adoc:1
#, no-wrap
msgid "Solar-Log"
msgstr "Solar-Log"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.pvinverter.solarlog.adoc:5
msgid "SymmetricPvInverter"
msgstr "SymmetricPvInverter"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.pvinverter.solarlog.adoc:7
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.pvinverter.solarlog[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.pvinverter.solarlog [Source Code icon: github []]"

#. type: Title =
#: edge/device_service.adoc.d/io.openems.edge.timedata.influxdb.adoc:1
#, no-wrap
msgid "InfluxDB"
msgstr "InfluxDB"

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.timedata.influxdb.adoc:4
msgid "Persists all data of OpenEMS Edge Channels to an InfluxDB timeseries database."
msgstr "Behält alle Daten von OpenEMS Edge Channels in einer InfluxDB-Zeitreihendatenbank bei."

#. type: Plain text
#: edge/device_service.adoc.d/io.openems.edge.timedata.influxdb.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.timedata.influxdb[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.timedata.influxdb [Source Code icon: github []]"

#. type: Title =
#: edge/implement.adoc:1
#, no-wrap
msgid "Implementing a device"
msgstr "Einführung eines Gerätes"

#. type: Title ==
#: edge/implement.adoc:12
#, no-wrap
msgid "Step-by-step guide"
msgstr "Schritt-für-Schritt-Leitfaden"

#. type: Plain text
#: edge/implement.adoc:15
msgid "This chapter explains the steps required to implement a meter in OpenEMS Edge that is connected via Modbus/TCP. The meter itself is simulated using a small Modbus slave application, so no external hardware is required for this guide."
msgstr "In diesem Kapitel werden die Schritte erläutert, die erforderlich sind, um einen Zähler in OpenEMS Edge zu implementieren, der über Modbus/TCP verbunden ist. Der Zähler selbst wird mit einer kleinen Modbus-Slave-Anwendung simuliert, so dass für diese Anleitung keine externe Hardware benötigt wird."

#. type: Plain text
#: edge/implement.adoc:17
msgid "The tutorial is based on the xref:gettingstarted.adoc[Getting Started] guide."
msgstr "Das Tutorial basiert auf dem Leitfaden xref: gettingstarted.adoc [Getting Started]."

#. type: Plain text
#: edge/implement.adoc:19
msgid "In this guides you will create different classes, these classes may create some errors. These errors will solve themselves within the progress of the guide because the classes are interdependent."
msgstr "In diesen Führungen werden verschiedene Klassen erstellt, diese Klassen können einige Fehler erzeugen. Diese Fehler lösen sich innerhalb des Fortschritts des Leitfadens, weil die Klassen voneinander abhängig sind."

#. type: Title ===
#: edge/implement.adoc:20
#, no-wrap
msgid "Create a new OSGi Bundle"
msgstr "Ein neues OSGi-Bundle erstellen"

#. type: Plain text
#: edge/implement.adoc:23
msgid "For more information see xref:coreconcepts.adoc#_osgi_bundle[OSGi Bundle]."
msgstr "Weitere Informationen finden Sie unter xref: coreconcepts.adoc # _ osgi _ bundle [OSGi Bundle]."

#. type: Plain text
#: edge/implement.adoc:25 edge/implement.adoc:126
msgid "In the menu choose btn:[File] -> btn:[New] -> btn:[Other]"
msgstr "Im Menü wählen btn: [Datei]-> btn: [Neu]-> btn: [Andere]"

#. type: Block title
#: edge/implement.adoc:26
#, no-wrap
msgid "Creating a new project in Eclipse IDE"
msgstr "Neues Projekt in Eclipse IDE erstellen"

#. type: Target for macro image
#: edge/implement.adoc:27
#, no-wrap
msgid "eclipse-file-new-other.png"
msgstr "eclipse-file-new-other.png"

#. type: Plain text
#: edge/implement.adoc:30
msgid "Select btn:[Bndtools] -> btn:[Bnd OSGi Project] and press btn:[Next >]"
msgstr "Wählen Sie btn: [Bndtools]-> btn: [Bnd OSGi Project] und drücken Sie btn: [Weiter >]"

#. type: Block title
#: edge/implement.adoc:31
#, no-wrap
msgid "Creating a Bnd OSGi Project in Eclipse IDE"
msgstr "Ein Bnd OSGi-Projekt in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:32
#, no-wrap
msgid "eclipse-bndtools-osgi-project.png"
msgstr "eclipse-bndtools-osgi-project.png"

#. type: Plain text
#: edge/implement.adoc:35
msgid "Select btn:[OSGi enRoute] -> btn:[Provider/Adapter Bundle] and press btn:[Next >]"
msgstr "Wählen Sie btn: [OSGi enRoute]-> btn: [Provider/Adapter Bundle] und drücken btn: [Weiter >]"

#. type: Plain text
#: edge/implement.adoc:37
msgid "Technically an OpenEMS Edge Device provides implementations of the interfaces of an OSGi _API Bundle_. In OSGi terminology this is called a _Provider/Adapter Bundle_"
msgstr "Technisch bietet ein OpenEMS Edge Device Implementierungen der Schnittstellen eines OSGi _ API Bundle _. In der OSGi-Terminologie wird dies als ein _ Provider/Adapter Bundle _ bezeichnet."

#. type: Block title
#: edge/implement.adoc:38
#, no-wrap
msgid "Creating a Bnd OSGi Provider/Adapter Bundle in Eclipse IDE"
msgstr "Erstellung eines Bnd OSGi Provider/Adapter-Bundle in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:39
#, no-wrap
msgid "eclipse-new-osgi-provider-bundle.png"
msgstr "eclipse-new-osgi-provider-bundle.png"

#. type: Plain text
#: edge/implement.adoc:42
msgid "Choose a project name and press btn:[Next >]"
msgstr "Wählen Sie einen Projektnamen und drücken Sie btn: [Weiter >]"

#. type: Plain text
#: edge/implement.adoc:44
msgid "The project name is used as the folder name in OpenEMS source directory. The naming is up to you, but it is good practice to keep the name lower case and use something like *io.openems.[edge/backend].[purpose/nature].[implementation]*. For the simulated meter `io.openems.edge.meter.simulated` is a good choice."
msgstr "Der Projektname wird als Ordnername im OpenEMS-Quellverzeichnis verwendet. Die Namensgebung liegt bei Ihnen, aber es ist eine gute Praxis, den Namen im Kleinbuchstaben zu behalten und so etwas wie * io.openems zu verwenden. [edge/backend]. [Purpose/nature]. [Umsetzung] *. Für den simulierten Zähler ist ' io.openems.edge.meter.simulated eine gute Wahl."

#. type: Block title
#: edge/implement.adoc:45
#, no-wrap
msgid "Naming a Bnd OSGi Provider/Adapter Bundle in Eclipse IDE"
msgstr "Benennung eines Bnd OSGi Provider/Adapter-Bundle in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:46
#, no-wrap
msgid "eclipse-new-osgi-provider-simulatedmeter.png"
msgstr "eclipse-new-osgi-provider-simulatedmeter.png"

#. type: Plain text
#: edge/implement.adoc:49
msgid "Accept defaults for the final screen and press btn:[Finish]"
msgstr "Nehmen Sie die Voreinstellungen für den letzten Bildschirm an und drücken Sie btn: [Ziel]"

#. type: Block title
#: edge/implement.adoc:50
#, no-wrap
msgid "Java settings for a Bnd OSGi Provider/Adapter Bundle in Eclipse IDE"
msgstr "Java-Einstellungen für ein Bnd OSGi Provider/Adapter-Bundle in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:51
#, no-wrap
msgid "eclipse-new-osgi-provider-simulatedmeter-final.png"
msgstr "eclipse-new-osgi-provider-simulatedmeter-final.png"

#. type: Plain text
#: edge/implement.adoc:54
msgid "The assistant closes and you can see your new bundle."
msgstr "Der Assistent schließt und Sie können Ihr neues Bündel sehen."

#. type: Title ===
#: edge/implement.adoc:55
#, no-wrap
msgid "Define Bundle dependencies"
msgstr "Bündel-Abhängigkeiten definieren"

#. type: Plain text
#: edge/implement.adoc:58
msgid "OSGi Bundles can be dependent on certain other Bundles. This information needs to be set in a *bnd.bnd* file."
msgstr "OSGi-Bundles können von bestimmten anderen Bundles abhängig sein. Diese Informationen müssen in einer * bnd.bnd *-Datei gesetzt werden."

#. type: Plain text
#: edge/implement.adoc:60
msgid "Select the component directory btn:[src] -> btn:[io.openems.edge.meter.simulated]"
msgstr "Wählen Sie das Komponentenverzeichnis btn: [src]-> btn: [io.openems.edge.meter.simulated]"

#. type: Block title
#: edge/implement.adoc:61
#, no-wrap
msgid "New simulated meter Bnd OSGi Provider/Adapter Bundle in Eclipse IDE"
msgstr "Neuer simulierter Zähler Bnd OSGi Provider/Adapter Bundle in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:62
#, no-wrap
msgid "eclipse-new-simulatedmeter-bundle.png"
msgstr "eclipse-new-simulatedmeter-bundle.png"

#. type: Plain text
#: edge/implement.adoc:65
msgid "Open the btn:[bnd.bnd] file by double clicking on it."
msgstr "Öffnen Sie die btn: [bnd.bnd]-Datei, indem Sie mit einem Doppelklick darauf klicken."

#. type: Plain text
#: edge/implement.adoc:67
msgid "Open the btn:[Build] tab"
msgstr "Öffnen Sie die tn: [Build] Tab"

#. type: Plain text
#: edge/implement.adoc:69
msgid "You can see, that the Bundle is currently dependent on a core OSGi API bundle ('osgi.enroute.base.api'). We are going to expand that list."
msgstr "Sie können sehen, dass das Bundle derzeit von einem Kern-OSGi-API-Bundle (' osgi.enroute.base.api ') abhängig ist. Wir werden diese Liste erweitern."

#. type: Block title
#: edge/implement.adoc:70
#, no-wrap
msgid "Bndtools Build configuration"
msgstr "Bndtools Build Konfiguration"

#. type: Target for macro image
#: edge/implement.adoc:71
#, no-wrap
msgid "eclipse-bnd-file-build.png"
msgstr "eclipse-bnd-file-build.png"

#. type: Plain text
#: edge/implement.adoc:74
msgid "Click the btn:[+] symbol next to *Build Path*."
msgstr "Klicken Sie auf das btn: [+]-Symbol neben * Bauen Sie Pfad *."

#. type: Block title
#: edge/implement.adoc:75
#, no-wrap
msgid "Bndtools Build Path configuration"
msgstr "Bndtools Build Path Konfiguration"

#. type: Target for macro image
#: edge/implement.adoc:76
#, no-wrap
msgid "eclipse-osgi-build-path.png"
msgstr "eclipse-osgi-build-path.png"

#. type: Plain text
#: edge/implement.adoc:79
msgid "Use the *Project Build Path* assistant to add the following Bundles as dependencies:"
msgstr "Verwenden Sie den * Project Build Path *-Assistenten, um die folgenden Bundles als Abhängigkeiten hinzuzufügen:"

#. type: Labeled list
#: edge/implement.adoc:80
#, no-wrap
msgid "io.openems.edge.common"
msgstr "io.openems.edge.common"

#. type: Plain text
#: edge/implement.adoc:82
msgid "The Edge Common Bundle provides implementations and services that are common to all OpenEMS Edge components."
msgstr "Das Edge Common Bundle bietet Implementierungen und Services, die allen OpenEMS Edge-Komponenten gemeinsam sind."

#. type: Labeled list
#: edge/implement.adoc:83
#, no-wrap
msgid "io.openems.edge.meter.api"
msgstr "io.openems.edge.meter.api"

#. type: Plain text
#: edge/implement.adoc:85
msgid "The Meter API Bundle provides the interfaces for OpenEMS Edge Meter Nature."
msgstr "Das Meter API Bundle bietet die Schnittstellen für OpenEMS Edge Meter Nature."

#. type: Labeled list
#: edge/implement.adoc:86
#, no-wrap
msgid "io.openems.edge.bridge.modbus"
msgstr "io.openems.edge.bridge.modbus"

#. type: Plain text
#: edge/implement.adoc:88
msgid "The Modbus Bundle provides the Bridge services for Modbus/RTU and Modbus/TCP protocols."
msgstr "Das Modbus Bundle bietet die Bridge-Dienste für Modbus/RTU und Modbus/TCP-Protokolle."

#. type: Plain text
#: edge/implement.adoc:90
msgid "It is also a good moment to configure the Bundle meta information. Still inside the btn:[bnd.bnd] file open the btn:[Source] tab. Add some meta information - it will help the users of your component:"
msgstr "Es ist auch ein guter Moment, um die Meta-Informationen von Bundle zu konfigurieren. Noch in der btn: [bnd.bnd]-Datei öffnen Sie die tn: [Source] Registerkarte."

#. type: delimited block -
#: edge/implement.adoc:102
#, no-wrap
msgid ""
"Bundle-Name: OpenEMS Edge Meter Simulated\n"
"Bundle-Vendor: OpenEMS Association e.V.\n"
"Bundle-License: https://opensource.org/licenses/EPL-2.0\n"
"Bundle-Version: 1.0.0.${tstamp}\n"
"Export-Package: \\\n"
"\tio.openems.edge.meter.api,\\\n"
"\tio.openems.edge.meter.asymmetric.api,\\\n"
"\tio.openems.edge.meter.symmetric.api\n"
"Private-Package: io.openems.edge.meter.simulated\n"
msgstr ""
"Bundle-Name: OpenEMS Edge Meter Simulated\n"
"Bundle-Vendor: OpenEMS Association e.V.\n"
"Bundle-License: https://opensource.org/licenses/EPL-2.0\n"
"Bundle-Version: 1.0.0.${tstamp}\n"
"Export-Paket: \\\n"
"\tio.openems.edge.meter.api,\\\n"
"\tio.openems.edge.meter.asymmetric.api,\\\n"
"\tio.openems.edge.meter.symmetric.api\n"
"Privatpaket: io.openems.edge.meter.simulated\n"

#. type: delimited block -
#: edge/implement.adoc:104
#, no-wrap
msgid "-includeresource: {readme.md}\n"
msgstr "-inkluderesource: {readme.md}\n"

#. type: delimited block -
#: edge/implement.adoc:112
#, no-wrap
msgid ""
"-buildpath: ${buildpath},\\\n"
"\tio.openems.common;version=latest,\\\n"
"\tio.openems.edge.common;version=latest,\\\n"
"\tio.openems.edge.controller.api;version=latest,\\\n"
"\tio.openems.edge.ess.api;version=latest,\\\n"
"\tcom.google.guava,\\\n"
"\tslf4j.api\n"
msgstr ""
"-buildpath: $ {buildpath}, \\\n"
"\tio.openems.common; Version = aktuell, \\\n"
"\tio.openems.edge.common; Version = aktuell, \\\n"
"\tio.openems.edge.controller.api; Version = aktuell, \\\n"
"\tio.openems.edge.ess.api; Version = aktuell, \\\n"
"\tcom.google.guava,\\\n"
"\tSlf4j.api\n"

#. type: delimited block -
#: edge/implement.adoc:114
#, no-wrap
msgid "-testpath: ${testpath}\n"
msgstr "-testpath: $ {testpath}\n"

#. type: delimited block -
#: edge/implement.adoc:117
#, no-wrap
msgid ""
"javac.source: 1.8\n"
"javac.target: 1.8\n"
msgstr ""
"javac.source: 1.8\n"
"javac.target: 1,8\n"

#. type: Title ===
#: edge/implement.adoc:119
#, no-wrap
msgid "Define configuration parameters"
msgstr "Konfigurationsparameter festlegen"

#. type: Plain text
#: edge/implement.adoc:122
msgid "OpenEMS Components can have several configuration parameters. They are defined as Java annotations and specific OSGi annotations are used to generate meta information that is used e.g. by Apache Felix Web Console to generate a user interface form (see xref:gettingstarted.adoc[Getting Started])."
msgstr "OpenEMS Components kann mehrere Konfigurationsparameter haben. Sie werden als Java-Anmerkungen definiert und spezifische OSGi-Anmerkungen werden verwendet, um Meta-Informationen zu generieren, die z.B. von Apache Felix Web Console verwendet werden, um ein Benutzeroberfläche-Formular zu generieren (siehe xref: gettingingstarted.adoc [Getting Started])."

#. type: Plain text
#: edge/implement.adoc:124
msgid "Make sure that the component directory is still selected."
msgstr "Stellen Sie sicher, dass das Komponentenverzeichnis noch ausgewählt ist."

#. type: Plain text
#: edge/implement.adoc:128
msgid "Select btn:[Java] -> btn:[Other...] and press btn:[Next >]"
msgstr "Wählen Sie btn: [Java]-> btn: [Other ...] and press btn: [Next >]"

#. type: Block title
#: edge/implement.adoc:129
#, no-wrap
msgid "Creating a Java annotation in Eclipse IDE"
msgstr "Erstellung einer Java-Notiz in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:130
#, no-wrap
msgid "eclipse-new-annotation.png"
msgstr "eclipse-new-annotation.png"

#. type: Plain text
#: edge/implement.adoc:133
msgid "Set the name *Config* press btn:[Finish]."
msgstr "Setzen Sie den Namen * Config * press btn: [Finish]."

#. type: Block title
#: edge/implement.adoc:134
#, no-wrap
msgid "Creating the Java annotation 'Config' in Eclipse IDE"
msgstr "Erstellung der Java-Anmerkung ' Config ' in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:135
#, no-wrap
msgid "eclipse-new-config-annotation.png"
msgstr "eclipse-new-config-annotation.png"

#. type: Plain text
#: edge/implement.adoc:138
msgid "A Java annotation template was generated for you:"
msgstr "Für Sie wurde eine Java-Anmerkungen-Vorlage erstellt:"

#. type: delimited block -
#: edge/implement.adoc:142 edge/implement.adoc:153 edge/implement.adoc:212
#, no-wrap
msgid "package io.openems.edge.meter.simulated;\n"
msgstr "Paket io.openems.edge.meter.simulated;\n"

#. type: delimited block -
#: edge/implement.adoc:144
#, no-wrap
msgid "public interface Config {\n"
msgstr "Öffentliche Schnittstelle Config {\n"

#. type: delimited block -
#: edge/implement.adoc:146
#, no-wrap
msgid "}\n"
msgstr "}\n"

#. type: Plain text
#: edge/implement.adoc:149
msgid "Adjust the template to match the following code:"
msgstr "Passen Sie die Vorlage an den folgenden Code an:"

#. type: delimited block -
#: edge/implement.adoc:156
#, no-wrap
msgid ""
"import org.osgi.service.metatype.annotations.AttributeDefinition;\n"
"import org.osgi.service.metatype.annotations.ObjectClassDefinition;\n"
msgstr ""
"org.osgi.service.metatype.annotations.AttributeDefinition importieren;\n"
"org.osgi.service.metatype.annotations.ObjectClassDefinition importieren;\n"

#. type: delimited block -
#: edge/implement.adoc:158
#, no-wrap
msgid "import io.openems.edge.meter.api.MeterType;\n"
msgstr "io.openems.edge.meter.api.MeterType importieren;\n"

#. type: delimited block -
#: edge/implement.adoc:163
#, no-wrap
msgid ""
"@ObjectClassDefinition( // <1>\n"
"\t\tname = \"Meter Simulated\", //\n"
"\t\tdescription = \"Implements the simulated meter.\")\n"
"@interface Config {\n"
msgstr ""
"@ObjectClassDefinition (/<1>\n"
"\t\tName = \"Meter Simulated\",//\n"
"\t\tBeschreibung = \"Implements the simulated meter.\")\n"
"@interface Config {\n"

#. type: delimited block -
#: edge/implement.adoc:166
#, no-wrap
msgid ""
"\t@AttributeDefinition(name = \"Component-ID\", description = \"Unique ID of this Component\")\n"
"\tString id() default \"meter0\"; // <2>\n"
msgstr ""
"@AttributeDefinition (Name = \"Component-ID\", Beschreibung = \"Einzigartige ID dieser Komponente\")\n"
"\tString id () default \"meter0\"; <2>\n"

#. type: delimited block -
#: edge/implement.adoc:169
#, no-wrap
msgid ""
"\t@AttributeDefinition(name = \"Alias\", description = \"Human-readable name of this Component; defaults to Component-ID\")\n"
"\tString alias() default \"\"; // <3>\n"
msgstr ""
"@AttributeDefinition (Name = \"Alias\", Beschreibung = \"Menschenlesbarer Name dieser Komponente; Standardeinstellungen auf Component-ID \")\n"
"\tString alias () default \"\"; <3>\n"

#. type: delimited block -
#: edge/implement.adoc:172
#, no-wrap
msgid ""
"\t@AttributeDefinition(name = \"Is enabled?\", description = \"Is this Component enabled?\")\n"
"\tboolean enabled() default true; // <4>\n"
msgstr ""
"@AttributeDefinition (Name = \"Ist aktiviert?\", Beschreibung = \"Ist diese Komponente aktiviert?\")\n"
"\tBoolean enabled () default true; <4>\n"

#. type: delimited block -
#: edge/implement.adoc:175
#, no-wrap
msgid ""
"\t@AttributeDefinition(name = \"Meter-Type\", description = \"Grid, Production (=default), Consumption\") // <5>\n"
"\tMeterType type() default MeterType.PRODUCTION; // <6>\n"
msgstr ""
"@AttributeDefinition (Name = \"Meter-Typ\", Beschreibung = \"Grid, Produktion (= Standard), Konsum\")//<5>\n"
"\tMeterType Typ () Standard MeterType.PRODUCTION; <6>\n"

#. type: delimited block -
#: edge/implement.adoc:178
#, no-wrap
msgid ""
"\t@AttributeDefinition(name = \"Modbus-ID\", description = \"ID of Modbus bridge.\")\n"
"\tString modbus_id(); // <7>\n"
msgstr ""
"@AttributeDefinition (Name = \"Modbus-ID\", Beschreibung = \"ID of Modbus bridge.\")\n"
"\tString modbus _ id (); <7>\n"

#. type: delimited block -
#: edge/implement.adoc:181
#, no-wrap
msgid ""
"\t@AttributeDefinition(name = \"Modbus Unit-ID\", description = \"The Unit-ID of the Modbus device.\")\n"
"\tint modbusUnitId(); // <8>\n"
msgstr ""
"@AttributeDefinition (Name = \"Modbus Unit-ID\", Beschreibung = \"Die Einheit-ID des Modbus-Gerätes.\")\n"
"\tint modbusUnitId (); <8>\n"

#. type: delimited block -
#: edge/implement.adoc:184
#, no-wrap
msgid ""
"\t@AttributeDefinition(name = \"Modbus target filter\", description = \"This is auto-generated by 'Modbus-ID'.\")\n"
"\tString Modbus_target() default \"\"; // <9>\n"
msgstr ""
"@AttributeDefinition (Name = \"Modbus-Zielfilter\", Beschreibung = \"Das wird automatisch generiert durch ' Modbus-ID '.\")\n"
"\tString Modbus _ target () default \"\"; <9>\n"

#. type: delimited block -
#: edge/implement.adoc:187
#, no-wrap
msgid ""
"\tString webconsole_configurationFactory_nameHint() default \"Meter Simulated [{id}]\"; // <10>\n"
"}\n"
msgstr ""
"String webconsole _ configurationFactory _ namehint () default \"Meter Simulated [{id}]\"; <10>\n"
"}\n"

#. TODO add screenshot that shows how the strings are used in Apache
#. type: Plain text
#: edge/implement.adoc:190
msgid "The *@ObjectClassDefinition* annotation defines this file as a Meta Type Resource for OSGi configuration admin. Use it to set a _name_ and _description_ for this OpenEMS Component."
msgstr "Die * @ObjectClassDefinition *-Anmerkung definiert diese Datei als Meta-Type-Ressource für OSGi-Konfigurationsadmin. Verwenden Sie es, um a _ name _ and _ description _ für diese OpenEMS-Komponente zu setzen."

#. type: Plain text
#: edge/implement.adoc:191
msgid "The *id* configuration parameter sets the OpenEMS Component-ID (see xref:coreconcepts.adoc[Channel Adress]). _Note_: A *default* ID 'meter0' is defined. It is good practice to define such an ID here, as it simplifies configuration in the UI."
msgstr "Der * id *-Konfigurationsparameter setzt die OpenEMS Component-ID (siehe xref: coreconcepts.adoc [Channel Adress]). _ Note _: A * default * ID ' meter0 ' ist definiert. Es ist eine gute Praxis, eine solche ID hier zu definieren, da sie die Konfiguration im UI vereinfacht."

#. type: Plain text
#: edge/implement.adoc:192
msgid "The *alias* configuration parameter sets the human-readable name of this OpenEMS Component. If no alias is configured, the Component-ID is used instead."
msgstr "Der * alias *-Konfigurationsparameter legt den menschlich lesbaren Namen dieser OpenEMS-Komponente fest. Wenn kein Alias konfiguriert ist, wird stattdessen die Component-ID verwendet."

#. type: Plain text
#: edge/implement.adoc:193
msgid "The *enabled* parameter provides a _soft_ way of deactivating an OpenEMS Component programmatically."
msgstr "Der * enabled *-Parameter bietet eine _ soft _ Möglichkeit, eine OpenEMS-Komponente programmatisch zu deaktivieren."

#. type: Plain text
#: edge/implement.adoc:194
msgid "The *@AttributeDefinition* annotation provides meta information about a configuration parameter like _name_ and _description_."
msgstr "Die * @AttributeDefinition *-Annotation liefert Meta-Informationen über einen Konfigurationsparameter wie _ name _ and _ description _."

#. type: Plain text
#: edge/implement.adoc:195
msgid "The 'Meter' nature requires definition of a MeterType that defines the purpose of the Meter. We will let the user define this type by a configuration parameter."
msgstr "Die ' Meter '-Natur erfordert die Definition eines MeterType, der den Zweck des Meters definiert. Wir lassen den Benutzer diesen Typ durch einen Konfigurationsparameter definieren."

#. type: Plain text
#: edge/implement.adoc:196
msgid "The 'Modbus-ID' parameter creates the link to a Modbus-Service via its OpenEMS Component-ID. At runtime the user will typically set this configuration parameter to something like 'modbus0'."
msgstr "Der Parameter \"Modbus-ID\" erstellt den Link zu einem Modbus-Service über seine OpenEMS Component-ID. Zur Laufzeit wird der Benutzer diesen Konfigurationsparameter typischerweise auf so etwas wie ' modbus0 ' setzen."

#. type: Plain text
#: edge/implement.adoc:197
msgid "The Modbus service implementation requires us to provide the Modbus _Unit-ID_ (also commonly called _Device-ID_ or _Slave-ID_) of the Modbus slave device. This is the ID that is configured at the simulated meter."
msgstr "Die Modbus-Service-Implementierung verlangt von uns, dass wir die Modbus _ Unit-ID _ (auch _ Device-ID _ oder _ Slave-ID _) des Modbus-Slave-Geräts zur Verfügung stellen. Dies ist die ID, die am simulierten Meter konfiguriert ist."

#. type: Plain text
#: edge/implement.adoc:198
msgid "The *Modbus_target* will be automatically set by OpenEMS framework and does usually not need to be configured by the user. _Note_: Linking other OpenEMS Components is implemented using OSGi References. The OpenEMS Edge framework therefor sets the 'target' property of a reference to filter the matched services."
msgstr "Das * Modbus _ target * wird automatisch durch OpenEMS-Framework gesetzt und muss in der Regel nicht vom Benutzer konfiguriert werden. _ Note _: Die Verknüpfung anderer OpenEMS-Komponenten wird mit OSGi-Referenzen implementiert. Das OpenEMS Edge-Framework legt damit die \"Zieleigenschaft\" eines Referenzbezugs fest, um die passenden Dienste zu filtern."

#. type: Plain text
#: edge/implement.adoc:199
msgid "The *webconsole_configurationFactory_nameHint* parameter sets a custom name for Apache Felix Web Console, helping the user to find the correct bundle."
msgstr "Der Parameter * webconsole _ configurationFactory _ nameHint * legt einen eigenen Namen für Apache Felix Web Console fest, der dem Benutzer hilft, das richtige Paket zu finden."

#. type: Title ===
#: edge/implement.adoc:200
#, no-wrap
msgid "Implement the OpenEMS Component"
msgstr "Die OpenEMS-Komponente umsetzen"

#. type: Plain text
#: edge/implement.adoc:203
msgid "The Bndtools assistant created a `ProviderImpl.java` file. First step is to set a proper name for this file. To rename the file, select it by clicking on it and choose btn:[Refactor] -> btn:[Rename...] in the menu. Write `MeterSimulated` as 'New name' and press btn:[Finish]."
msgstr "Der Bndtools-Assistent hat eine ' ProviderImpl.java '-Datei erstellt. Der erste Schritt ist, einen richtigen Namen für diese Datei festzulegen. Um die Datei umzubenennen, wählen Sie sie aus, indem Sie darauf klicken und btn: [Refactor]-> btn: [Umbenennen ...] im Menü. Schreiben Sie ' MeterSimulated ' als ' Neuer Name ' und drücken Sie btn: [Finish]."

#. type: Block title
#: edge/implement.adoc:204
#, no-wrap
msgid "Renaming a Java class in Eclipse IDE"
msgstr "Umbenennung einer Java-Klasse in Eclipse IDE"

#. type: Target for macro image
#: edge/implement.adoc:205
#, no-wrap
msgid "eclipse-rename.png"
msgstr "eclipse-rename.png"

#. type: Plain text
#: edge/implement.adoc:208
msgid "Afterwards replace the content of `MeterSimulated.java` file with the following code:"
msgstr "Danach ersetzen Sie den Inhalt der ' MeterSimulated.java '-Datei durch folgenden Code:"

#. type: delimited block -
#: edge/implement.adoc:224
#, no-wrap
msgid ""
"import org.osgi.service.cm.ConfigurationAdmin;\n"
"import org.osgi.service.component.ComponentContext;\n"
"import org.osgi.service.component.annotations.Activate;\n"
"import org.osgi.service.component.annotations.Component;\n"
"import org.osgi.service.component.annotations.ConfigurationPolicy;\n"
"import org.osgi.service.component.annotations.Deactivate;\n"
"import org.osgi.service.component.annotations.Reference;\n"
"import org.osgi.service.component.annotations.ReferenceCardinality;\n"
"import org.osgi.service.component.annotations.ReferencePolicy;\n"
"import org.osgi.service.component.annotations.ReferencePolicyOption;\n"
"import org.osgi.service.metatype.annotations.Designate;\n"
msgstr ""
"org.osgi.service.cm.ConfigurationAdmin importieren;\n"
"org.osgi.service.component.ComponentContext importieren;\n"
"org.osgi.service.component.annotations.Activate importieren;\n"
"org.osgi.service.component.annotations.Component importieren;\n"
"org.osgi.service.component.annotations.ConfigurationPolicy importieren;\n"
"org.osgi.service.component.annotations.Deactivate importieren;\n"
"org.osgi.service.component.annotations.Reference importieren;\n"
"org.osgi.service.component.annotations.ReferenceCardinality importieren;\n"
"org.osgi.service.component.annotations.ReferencePolicy importieren;\n"
"org.osgi.service.component.annotations.ReferencePolicyOption importieren;\n"
"org.osgi.service.metatype.annotations.Designate importieren;\n"

#. type: delimited block -
#: edge/implement.adoc:235
#, no-wrap
msgid ""
"import io.openems.edge.bridge.modbus.api.AbstractOpenemsModbusComponent;\n"
"import io.openems.edge.bridge.modbus.api.BridgeModbus;\n"
"import io.openems.edge.bridge.modbus.api.ModbusProtocol;\n"
"import io.openems.edge.bridge.modbus.api.element.SignedWordElement;\n"
"import io.openems.edge.bridge.modbus.api.task.FC3ReadRegistersTask;\n"
"import io.openems.edge.common.channel.doc.Doc;\n"
"import io.openems.edge.common.component.OpenemsComponent;\n"
"import io.openems.edge.common.taskmanager.Priority;\n"
"import io.openems.edge.meter.api.MeterType;\n"
"import io.openems.edge.meter.api.SymmetricMeter;\n"
msgstr ""
"io.openems.edge.bridge.modbus.api.AbstractOpenemsModbusComponent importieren;\n"
"io.openems.edge.bridge.modbus.api.BridgeModbus importieren;\n"
"io.openems.edge.bridge.modbus.api.ModbusProtocol importieren;\n"
"io.openems.edge.bridge.modbus.api.element.SignedWordElement importieren;\n"
"io.openems.edge.bridge.modbus.api.task.FC3ReadRegistersTask importieren;\n"
"io.openems.edge.common.channel.doc.Doc importieren;\n"
"io.openems.edge.common.component.OpenemsComponent importieren;\n"
"io.openems.edge.common.taskmanager.Priority importieren;\n"
"io.openems.edge.meter.api.MeterType importieren;\n"
"io.openems.edge.meter.api.SymmetricMeter importieren;\n"

#. type: delimited block -
#: edge/implement.adoc:243
#, no-wrap
msgid ""
"@Designate(ocd = Config.class, factory = true) // <1>\n"
"@Component( // <2>\n"
"\tname = \"Meter.Simulated\", // <3>\n"
"\timmediate = true, // <4>\n"
"\tconfigurationPolicy = ConfigurationPolicy.REQUIRE) // <5>\n"
"public class MeterSimulated extends AbstractOpenemsModbusComponent // <6>\n"
"\timplements SymmetricMeter, OpenemsComponent { // <7>\n"
msgstr ""
"@Designate (ocd = Config.class, factory = true)//<1>\n"
"@Component (/<2>\n"
"\tName = \"Meter.Simulated\",/<3>\n"
"\tSofort = true,//<4>\n"
"\tKonfigurationPolicy = ConfigurationPolicy.REQUIRE)//<5>\n"
"Öffentliche Klasse MeterSimulated erweitert AbstractOpenemsModbusComponent//<6>\n"
"\tImplementiert SymmetricMeter, OpenemsComponent {//<7>\n"

#. type: delimited block -
#: edge/implement.adoc:245
#, no-wrap
msgid "\tprivate MeterType meterType = MeterType.PRODUCTION;\n"
msgstr "Private MeterType meterType = MeterType.PRODUCTION;\n"

#. type: delimited block -
#: edge/implement.adoc:248
#, no-wrap
msgid ""
"\t@Reference\n"
"\tprotected ConfigurationAdmin cm; // <8>\n"
msgstr ""
"@Reference\n"
"\tSchutze <8>\n"

#. type: delimited block -
#: edge/implement.adoc:255
#, no-wrap
msgid ""
"\tpublic MeterSimulated() {\n"
"\t\tsuper(// <9>\n"
"\t\t\t\tOpenemsComponent.ChannelId.values(), //\n"
"\t\t\t\tSymmetricMeter.ChannelId.values() //\n"
"\t\t);\n"
"\t}\n"
msgstr ""
"Public MeterSimulated () {\n"
"\t\tSuper (/<9>\n"
"\t\t\t\tOpenemsComponent.ChannelId.values (),//\n"
"\t\t\t\tSymmetricMeter.ChannelId.values ()//\n"
"\t\t);\n"
"\t}\n"

#. type: delimited block -
#: edge/implement.adoc:260
#, no-wrap
msgid ""
"\t@Reference(policy = ReferencePolicy.STATIC, policyOption = ReferencePolicyOption.GREEDY, cardinality = ReferenceCardinality.MANDATORY)\n"
"\tprotected void setModbus(BridgeModbus modbus) {\n"
"\t\tsuper.setModbus(modbus); // <10>\n"
"\t}\n"
msgstr ""
"@Reference (policy = ReferencePolicy.STATIC, policyoption = ReferencePolicyOption.GREEDY, cardinality = ReferenceCardinality.MANDATORY)\n"
"\tGeschützt ist levot Modbus (BridgeModbus modbus) {\n"
"\t\tsuper.setModbus (modbus); <10>\n"
"\t}\n"

#. type: delimited block -
#: edge/implement.adoc:264
#, no-wrap
msgid ""
"\t@Activate\n"
"\tvoid activate(ComponentContext context, Config config) { // <11>\n"
"\t\tthis.meterType = config.type();\n"
msgstr ""
"@Activate\n"
"\tLeere Aktivierung (ComponentContext-Kontext, Config config) {//<11>\n"
"\t\tThis.meterType = config.type ();\n"

#. type: delimited block -
#: edge/implement.adoc:268
#, no-wrap
msgid ""
"\t\tsuper.activate(context, config.id(), config.alias(), config.enabled(), config.modbusUnitId(), this.cm,\n"
"\t\t\t\t\"Modbus\", config.modbus_id());\n"
"\t}\n"
msgstr ""
"Super.activate (context, config.id (), config.alias (), config.enabled (), config.modbusUnitId (), this.cm,\n"
"\t\t\t\t\"Modbus\", config.modbus _ id ());\n"
"\t}\n"

#. type: delimited block -
#: edge/implement.adoc:273
#, no-wrap
msgid ""
"\t@Deactivate\n"
"\tprotected void deactivate() { // <12>\n"
"\t\tsuper.deactivate();\n"
"\t}\n"
msgstr ""
"@Deactivate\n"
"\tGeschützte Leere deaktivieren () {//<12>\n"
"\t\tSuper.deaktivieren ();\n"
"\t}\n"

#. type: delimited block -
#: edge/implement.adoc:277
#, no-wrap
msgid ""
"\tpublic enum ChannelId implements io.openems.edge.common.channel.doc.ChannelId { // <13>\n"
"\t\t;\n"
"\t\tprivate final Doc doc;\n"
msgstr ""
"Public enum ChannelId implementiert io.openems.edge.common.channel.doc.ChannelId {//<13>\n"
"\t\t;\n"
"\t\tPrivates Finale Doc;\n"

#. type: delimited block -
#: edge/implement.adoc:281
#, no-wrap
msgid ""
"\t\tprivate ChannelId(Doc doc) {\n"
"\t\t\tthis.doc = doc;\n"
"\t\t}\n"
msgstr ""
"Privat-Kanäle (Doc doc) {\n"
"\t\t\tThis.doc = doc;\n"
"\t\t}\n"

#. type: delimited block -
#: edge/implement.adoc:286
#, no-wrap
msgid ""
"\t\tpublic Doc doc() {\n"
"\t\t\treturn this.doc;\n"
"\t\t}\n"
"\t}\n"
msgstr ""
"public doc doc () {\n"
"\t\t\tZurück this.doc;\n"
"\t\t}\n"
"\t}\n"

#. type: delimited block -
#: edge/implement.adoc:291
#, no-wrap
msgid ""
"\t@Override\n"
"\tpublic MeterType getMeterType() { // <14>\n"
"\t\treturn this.meterType;\n"
"\t}\n"
msgstr ""
"@Override\n"
"\tpublic MeterType getMeterType () {//<14>\n"
"\t\tRückseite this.meterType;\n"
"\t}\n"

#. type: delimited block -
#: edge/implement.adoc:298
#, no-wrap
msgid ""
"\t@Override\n"
"\tprotected ModbusProtocol defineModbusProtocol() throws OpenemsException { // <15>\n"
"\t\treturn new ModbusProtocol(this, // <16>\n"
"\t\t\t\tnew FC3ReadRegistersTask(1000, Priority.HIGH, // <17>\n"
"\t\t\t\t\t\tm(SymmetricMeter.ChannelId.ACTIVE_POWER, new SignedWordElement(1000)))); // <18>\n"
"\t}\n"
msgstr ""
"@Override\n"
"\tDas <15>\n"
"\t\tNeues ModbusProtocol (this,//<16>\n"
"\t\t\t\tNeue FC3ReadRegistersTask-1000, Priority.HIGH,//<17>\n"
"\t\t\t\t\t\tm (SymmetricMeter.ChannelId.ACTIVE _ POWER, new SignedWordElement-1000)))); <18>\n"
"\t}\n"

#. type: delimited block -
#: edge/implement.adoc:304
#, no-wrap
msgid ""
"\t@Override\n"
"\tpublic String debugLog() { // <19>\n"
"\t\treturn \"L:\" + this.getActivePower().value().asString();\n"
"\t}\n"
"}\n"
msgstr ""
"@Override\n"
"\tpublic String debugLog () {//<19>\n"
"\t\tReturn \"L:\" + this.getActivePower () .value () .asString ();\n"
"\t}\n"
"}\n"

#. type: Plain text
#: edge/implement.adoc:306
msgid "The *@Designate* annotation is used for OSGi to create a connection to the _Config_ annotation class. It also defines this Component as a _factory_, i.e. it can produce multiple instances with different configurations."
msgstr "Die * @Designate *-Anmerkung wird für OSGi verwendet, um eine Verbindung zur _ Config _ Anmerkunde-Klasse zu erstellen. Es definiert diese Komponente auch als _ factory _, d.h. es kann mehrere Instanzen mit unterschiedlichen Konfigurationen erzeugen."

#. type: Plain text
#: edge/implement.adoc:307
msgid "The *@Component* annotation marks this class as an OSGi component."
msgstr "Die * @Component *-Anmerkung markiert diese Klasse als OSGi-Komponente."

#. type: Plain text
#: edge/implement.adoc:308
msgid "The *name* property sets the unique name of this component. It is used to store configuration in the filesystem, to identify the component inside Apache Felix Web Console, and so on. Configure a human-readable name in the form *[nature].[vendor].[product]*."
msgstr "Die * name * Eigenschaft setzt den eindeutigen Namen dieser Komponente. Es wird verwendet, um die Konfiguration im Dateisystem zu speichern, um die Komponente innerhalb der Apache Felix Web-Konsole zu identifizieren und so weiter. Konfigurieren Sie einen menschlich lesbaren Namen in der Form * [Natur]. [Verkäufer]. [product] *."

#. type: Plain text
#: edge/implement.adoc:309
msgid "The *immediate* property defines whether the component should be started immediately. Configure the Component to be started immediately after configuration, i.e. it is not waiting till its service is required by another Component."
msgstr "Die * immediate *-Eigenschaft legt fest, ob die Komponente sofort gestartet werden soll. Konfigurieren Sie die Komponente, die unmittelbar nach der Konfiguration gestartet werden soll, d.h. sie wartet nicht, bis ihr Service von einem anderen Bauteil benötigt wird."

#. type: Plain text
#: edge/implement.adoc:310
msgid "The *configurationPolicy* define that the configuration of the Component is required before it gets activated."
msgstr "Die * Konfigurationspolitik * definiert, dass die Konfiguration der Komponente erforderlich ist, bevor sie aktiviert wird."

#. type: Plain text
#: edge/implement.adoc:311
msgid "To ease the implementation of a Modbus device we can extend the *AbstractOpenemsModbusComponent* class."
msgstr "Um die Implementierung eines Modbus-Geräts zu erleichtern, können wir die * AbstractOpenemsModbusComponent *-Klasse erweitern."

#. type: Plain text
#: edge/implement.adoc:313
msgid "If the device was using another protocol, it is advisable to use the *AbstractOpenemsComponent* class as a convenience layer instead of implementing everything required by the *OpenemsComponent* interface manually."
msgstr "Wenn das Gerät ein anderes Protokoll benutzte, ist es ratsam, die * AbstractOpenemsComponent *-Klasse als Komfortschicht zu verwenden, anstatt alles, was von der * OpenemsComponent *-Schnittstelle benötigt wird, manuell zu implementieren."

#. type: Plain text
#: edge/implement.adoc:315
msgid "The class implements *OpenemsComponent*. This makes it an xref:coreconcepts.adoc#_openems_component[OpenEMS Component].  The Device that we are is a *SymmetricMeter*. We already defined the required Channels in the _initializeChannels()_ method. Additionally the Component also needs to implement the Nature interface."
msgstr "Die Klasse implementiert * OpenemsComponent *. Das macht es zu einem xref: coreconcepts.adoc # _ openems _ component [OpenEMS Component].  Das Gerät, das wir sind, ist ein * SymmetricMeter *. Wir haben bereits die benötigten Kanäle in der _ initializeChannels () _ Methode definiert. Zusätzlich muss die Komponente auch die Natur-Schnittstelle implementieren."

#. type: Plain text
#: edge/implement.adoc:317
msgid "In plain Java it is not required to add `implements OpenemsComponent` if we inherit from 'AbstractOpenemsComponent' or 'AbstractOpenemsModbusComponent'. Be aware that for OSGi dependency injection to function properly, it is still required to mention all implemented interfaces again, as it is not considering the complete inheritance tree."
msgstr "In einem einfachen Java ist es nicht erforderlich, `implementiert OpenemsComponent` hinzuzufügen, wenn wir von 'AbstractOpenemsComponent' oder 'AbstractOpenemsModbusComponent' erben. Beachten Sie, dass es immer noch erforderlich ist, alle implementierten Schnittstellen erneut zu erwähnen, damit die Injizierung der OSGi-Abhängigkeiten richtig funktionieren kann, da sie nicht die komplette Vererbungshierarchie berücksichtigt."

#. type: Plain text
#: edge/implement.adoc:318
msgid "The `super.activate()` method requires an instance of *ConfigurationAdmin* as a parameter. Using the *@Reference* annotation the OSGi framework is going to provide the ConfigurationAdmin service via dependency injection."
msgstr "Die `super.activate()`-Methode benötigt eine Instanz von *ConfigurationAdmin* als Parameter. Mit der *@Reference* Annotation wird das OSGi-Framework den ConfigurationAdmin-Service injizieren."

#. type: Plain text
#: edge/implement.adoc:319
msgid ""
"We call the constructor of the super class (`AbstractOpenemsModbusComponent`/`AbstractOpenemsComponent`) to initialize the Channels of the Component. It is important to list all ChannelId-Enums of all implemented Natures. The call takes the *ChannelId* declarations and creates a Channel instance for each of them; e.g. for the `SymmetricMeter.ACTIVE_POWER` ChannelId, an object instance of `IntegerReadChannel` is created that "
"represents the Channel."
msgstr ""
"Wir rufen den Konstruktor der Super-Klasse (`AbstractOpenemsModbusComponent`/`AbstractOpenemsComponent`) auf, um die Channels der Komponente zu initialisieren. Es ist wichtig, alle ChannelId-Enums aller implementierten Natures aufzulisten. Der Aufruf nimmt die * ChannelId *-Deklarationen auf und erstellt für jede von ihnen eine Kanalinstanz; Zum Beispiel für den ' SymmetricMeter.ACTIVE _ POWER ' ChannelId wird eine "
"Objektinstanz von `IntegerReadChannel` erstellt, die den Channel repräsentiert."

#. type: Plain text
#: edge/implement.adoc:320
msgid ""
"The Component utilizes an external Modbus Component (the _Modbus Bridge_) for the actual Modbus communication. We receive an instance of this service via dependency injection (like we did already for the _ConfigurationAdmin_ service). Most of the magic is handled by the _AbstractOpenemsModbusComponent_ implementation, but the way the OSGi framework works, we need to define the _@Reference_ explicitly here in the actual "
"implementation of the component and call the parent `setModbus()` method."
msgstr ""
"Für die eigentliche Modbus-Kommunikation nutzt die Komponente eine externe Modbus-Komponente (die _ Modbus-Brücke _). Wir erhalten eine Instanz dieses Dienstes über die Abhängigkeitsspritze (wie wir es bereits für den _ConfigurationAdmin_ service getan haben). Der größte Teil der Magie wird durch die _AbstractOpenemsModbusComponent_ Implementation behandelt, aber die Art und Weise, wie das OSGi-Framework funktioniert, "
"müssen wir hier in der eigentlichen Implementierung der Komponente explizit definieren und die Eltern-Methode `setModbus()` nennen."

#. type: Plain text
#: edge/implement.adoc:321
msgid "The *activate()* method (marked by the *@Activate* annotation) is called on activation of an object instance of this Component. It comes with a ComponentContext and an instance of a configuration in the form of a Config object. All logic for activating and deactivating the OpenEMS Component is hidden in the super classes and just needs to be called from here."
msgstr "Die *activate()*-Methode (gekennzeichnet durch die *@Activate* Annotation) wird zur Aktivierung einer Objektinstanz dieser Komponente aufgerufen. Es kommt mit einem ComponentContext und einer Instanz einer Konfiguration in Form eines Config-Objekts. Alle Logik für die Aktivierung und Deaktivierung der OpenEMS-Komponente ist in den Superklassen versteckt und muss nur von hier aus aufgerufen werden."

#. type: Plain text
#: edge/implement.adoc:322
msgid "The *deactivate()* method (marked by the *@Deactivate* annotation) is called on deactivation of the Component instance."
msgstr "Die *deactivate()* Methode (gekennzeichnet durch die *@Deactivate* Annotation) wird zur Deaktivierung der Komponenteninstanz aufgerufen."

#. type: Plain text
#: edge/implement.adoc:323
msgid "The simulated implementation is only going to provide Channels defined by _OpenemsComponent_ and _SymmetricMeter_ natures. It is still good practice to add a skeleton for custom Channels to the Component implementation. We therefor add the _Channel Declaration_ block inside the class."
msgstr "Die simulierte Implementierung wird nur Kanäle liefern, die von _ OpenemsComponent _ und _ SymmetricMeter _ natures definiert werden. Es ist immer noch eine gute Praxis, ein Skelett für kundenspezifische Kanäle zur Komponenten-Implementierung hinzuzufügen. Wir fügen daher die _ Channel Declaration _ block in die Klasse ein."

#. type: delimited block =
#: edge/implement.adoc:327
msgid "Channel declarations are *enum* types implementing the ChannelId interface."
msgstr "Kanaldeklarationen sind * enum *-Typen, die die ChannelId-Schnittstelle implementieren."

#. type: delimited block =
#: edge/implement.adoc:328
msgid "This enum is empty, as we do not have custom Channels here."
msgstr "Dieses Enum ist leer, da wir hier keine eigenen Kanäle haben."

#. type: delimited block =
#: edge/implement.adoc:329
msgid "ChannelId enums require a Doc object that provides meta information about the Channel - e.g. the above ACTIVE_POWER Channel is defined as `ACTIVE_POWER(new Doc().type(OpenemsType.INTEGER).unit(Unit.WATT)`"
msgstr "ChannelId enums benötigen ein Doc-Objekt, das Meta-Informationen über den Kanal liefert-z.B. der obige ACTIVE _ POWER Channel wird als ' ACTIVE _ POWER (new Doc () .type (OpenemsType.INTEGER) .unit (Unit.WATT) ' definiert."

#. type: Plain text
#: edge/implement.adoc:331
msgid "The SymmetricMeter Nature requires us to provide a *MeterType* via a `MeterType getMeterType()` method. The MeterType is provided by the Config."
msgstr "Die SymmetricMeter-Natur verlangt von uns, dass wir eine * MeterType * über eine ' MeterType getMeterType ()-Methode anbieten. Der MeterType wird vom Config zur Verfügung gestellt."

#. type: Plain text
#: edge/implement.adoc:332
msgid "_AbstractOpenemsModbusComponent_ requires to implement a *defineModbusProtocol()* method that returns an instance of *ModbusProtocol*. The _ModbusProtocol_ class maps Modbus addresses to OpenEMS Channels and provides some conversion utilities. Instantiation of a _ModbusProtocol_ object uses the https://en.wikipedia.org/wiki/Builder_pattern#Java[Builder pattern icon:external-link[]]"
msgstr ""
"_ AbstractOpenemsModbusComponent _ benötigt die Implementierung einer * defineModbusProtocol () *-Methode, die eine Instanz von * ModbusProtocol * zurückgibt. Die _ ModbusProtocol _ class maps Modbus Adressen zu OpenEMS-Kanälen und stellt einige Konvertierungsprogramme zur Verfügung. Die Instantiation of a _ ModbusProtocol _ object verwendet das https://en.wikipedia.org/wiki/Builder_pattern#Java [Builder pattern icon:external-link[]]"

#. type: Plain text
#: edge/implement.adoc:333
msgid "Creates a *new ModbusProtocol* instance. A reference to the component itself is the first parameter, followed by an arbitrary number of 'Tasks' (implemented as a Java varags array)."
msgstr "Erstellt eine * neue ModbusProtocol *-Instanz. Eine Referenz auf die Komponente selbst ist der erste Parameter, gefolgt von einer beliebigen Anzahl von ' Tasks ' (implementiert als Java-varags-Array)."

#. type: Plain text
#: edge/implement.adoc:334
#, no-wrap
msgid "*FC3ReadRegistersTask* is an implementation of Modbus http://www.simplymodbus.ca/FC03.htm[function code 3 \"Read Holding Registers\" icon:external-link[]]. Its first parameter is the start address of the register block. The second parameter is a priority information that defines how often this register block needs to be queried. Following parameters are an arbitrary number of *ModbusElements*.\n"
msgstr "* FC3ReadRegistersTask * ist eine Implementierung von Modbus http://www.simplymodbus.ca/FC03.htm[function Code 3 \"Read Holding Registers\" icon:external-link[]]. Sein erster Parameter ist die Startadresse des Registerblocks. Der zweite Parameter ist eine Prioritätsinformation, die festlegt, wie oft dieser Registerblock abgefragt werden muss. Folgende Parameter sind eine beliebige Anzahl von * ModbusElements *.\n"

#. type: Plain text
#: edge/implement.adoc:336
msgid "Most Modbus function codes are available by their respective _FC*_ implementation classes."
msgstr "Die meisten Modbus-Funktionscodes sind von den jeweiligen _ FC * _ Implementierungsklassen verfügbar."

#. type: Plain text
#: edge/implement.adoc:337
msgid "Here the internal *m()* method is used to make a simple 1-to-1 mapping between the Modbus element at address `1000` and the Channel _SymmetricMeter.ChannelId.ACTIVE_POWER_. The Modbus element is defined as a 16 bit word element with an signed integer value."
msgstr "Hier wird die interne * m () *-Methode verwendet, um eine einfache 1-zu-1-Mapping zwischen dem Modbus-Element unter der Adresse ' 1000 ' und dem Channel _ SymmetricMeter.ChannelId.ACTIVE _ POWER _ zu erstellen. Das Modbus-Element wird als ein 16-Bit-Wort-Element mit einem signierten Integer-Wert definiert."

#. type: delimited block =
#: edge/implement.adoc:341
msgid "The _m()_ method also takes an instance of *ElementToChannelConverter* as an additional parameter. It can be used to add implicit unit conversions between Modbus element and OpenEMS Channel - like adding a scale factor that converts a read value of '95' to a channel value of '950'."
msgstr "Die _ m () _-Methode nimmt auch eine Instanz von * ElementToChannelConverter * als zusätzlichen Parameter. Es kann verwendet werden, um implizite Einheiten-Konvertierungen zwischen Modbus-Element und OpenEMS-Kanal hinzuzufügen-wie zum Beispiel einen Skalenfaktor, der einen Lesewert von ' 95 ' in einen Kanalwert von ' 950 ' umwandelt."

#. type: delimited block =
#: edge/implement.adoc:342
msgid "For Modbus registers that are empty or should be ignored, the *DummyRegisterElement* can be used."
msgstr "Für Modbus-Register, die leer sind oder ignoriert werden sollten, kann das * DummyRegisterElement * verwendet werden."

#. type: delimited block =
#: edge/implement.adoc:343
msgid "For more advanced channel-to-element mapping functionalities the internal *cm()* method can be used - e.g. to map one Modbus element to multiple Channels."
msgstr "Für fortgeschrittene Kanal-zu-Element-Mapping-Funktionen kann die interne * cm () *-Methode verwendet werden-z.B. um ein Modbus-Element auf mehrere Kanäle zu kartieren."

#. type: delimited block =
#: edge/implement.adoc:345
msgid "Using this principle a complete Modbus table consisting of multiple register blocks that need to be read or written with different Modbus function codes can be defined. For details have a look at the existing implementation classes inside the Modbus Bridge source code."
msgstr "Mit diesem Prinzip kann eine komplette Modbus-Tabelle definiert werden, die aus mehreren Registerblöcken besteht, die mit verschiedenen Modbus-Funktionscodes gelesen oder geschrieben werden müssen. Weitere Informationen finden Sie in den bestehenden Implementierungsklassen innerhalb des Quellcodes der Modbus Bridge."

#. type: delimited block =
#: edge/implement.adoc:346
msgid "Finally it is always a good idea to define a *debugLog()* method. This method is called in each cycle by the *Controller.Debug.Log* and very helpful for continuous debugging."
msgstr "Schließlich ist es immer eine gute Idee, eine * debugLog-Log-Log-Methode () * zu definieren. Diese Methode wird in jedem Zyklus von der * Controller.Debug.Log * genannt und sehr hilfreich für ein kontinuierliches Debugging."

#. type: Title ===
#: edge/implement.adoc:348
#, no-wrap
msgid "Start the device simulator"
msgstr "Starten Sie den Gerätesimulator"

#. type: Plain text
#: edge/implement.adoc:351
msgid "To start the device simulator, open the btn:[io.openems.edge.bridge.modbus] project and navigate to the btn:[test] -> btn:[io.openems.edge.brige.modbus] folder. There you find the btn:[ModbusSlaveSimulator.java] file. Right-click that file and select btn:[Run As] -> btn:[Java Application]."
msgstr "Um den Gerätesimulator zu starten, öffnen Sie das btn: [io.openems.edge.bridge.modbus]-Projekt und navigieren Sie zum Ordner btn: [test]-> btn: [io.openems.edge.brige.modbus]. Dort finden Sie die btn: [ModbusSlaveSimulator.java] Datei. Klicken Sie mit der rechten Maustaste auf die Datei und wählen Sie btn: [Run As] > btn: [Java Application]."

#. type: delimited block =
#: edge/implement.adoc:355
msgid "This _ModbusSlaveSimulator_ runs a small Modbus-TCP _Slave_-Server, that provides some constant values:"
msgstr "Dieser _ ModbusSlaveSimulator _ betreibt einen kleinen Modbus-TCP _ Slave _-Server, der einige konstante Werte liefert:"

#. type: delimited block =
#: edge/implement.adoc:362
#, no-wrap
msgid ""
"|===\n"
"|Address |Constant value\n"
"|1000    |500\n"
"|1001    |100\n"
"|2000    |123\n"
"|===\n"
msgstr ""
"|===\n"
"| Adresse | Konstanter Wert\n"
"1000 | 500\n"
"| 1001 | 100\n"
"| 2000 | 123\n"
"|===\n"

#. type: delimited block =
#: edge/implement.adoc:364
msgid "In the end of this guide, you will see a production of '500 W' - where '500' comes from register address 1000."
msgstr "Am Ende dieses Leitfadens sehen Sie eine Produktion von ' 500 W '-wobei ' 500 ' von der Registeradresse 1000 stammt."

#. type: Title ===
#: edge/implement.adoc:366
#, no-wrap
msgid "Enable the Component"
msgstr "Die Komponente aktivieren"

#. type: Plain text
#: edge/implement.adoc:369
msgid "To enable the Component for running, open the btn:[io.openems.edge.application] project and open the btn:[EdgeApp.bndrun] file. Change to the btn:[Source] view and create two new lines to declare the new Component."
msgstr "Um die Komponente für das Laufen zu aktivieren, öffnen Sie das btn: [io.openems.edge.application]-Projekt und öffnen Sie die btn: [EdgeApp.bndrun]-Datei. Ändern Sie die btn: [Quelle]-Ansicht und erstellen Sie zwei neue Zeilen, um die neue Komponente zu deklarieren."

#. type: Plain text
#: edge/implement.adoc:372
msgid "First: somewhere below *-runrequires: \\* add `osgi.identity;filter:='(osgi.identity=io.openems.edge.meter.simulated)',\\`"
msgstr "Erstens: Irgendwo unter *-runrequires: \\ * add ' osgi.identity; filter: = ' (osgi.identity = io.openems.edge.meter.simulated) ', \\ '"

#. type: Plain text
#: edge/implement.adoc:375
msgid "Second: somewhere below *-runbundles: \\* add `io.openems.edge.meter.simulated;version=snapshot,\\`"
msgstr "Zweitens: Irgendwo unter *-runbundles: \\ * Add ' io.openems.edge.meter.simulated; version = Snapshot, \\ '"

#. type: Plain text
#: edge/implement.adoc:377
msgid "You may have found, that the entries are sorted alphabetically."
msgstr "Sie haben vielleicht festgestellt, dass die Einträge alphabetisch sortiert sind."

#. type: Title ===
#: edge/implement.adoc:378
#, no-wrap
msgid "Run the implementation"
msgstr "Umsetzung laufen"

#. type: Plain text
#: edge/implement.adoc:381
msgid "Switch back to btn:[Run] view and press btn:[Run OSGi] to run OpenEMS Edge."
msgstr "Wechseln Sie zurück zu btn: [Run]-Ansicht und drücken Sie ttn: [Run OSGi], um OpenEMS Edge laufen zu lassen."

#. type: Plain text
#: edge/implement.adoc:386
msgid "From then you can configure your component as shown in xref:gettingstarted.adoc[Getting Started] guide. Add the following configurations inside Apache Felix Web Console:"
msgstr "Ab diesem Zeitpunkt können Sie Ihre Komponente so konfigurieren, wie sie in xref: gettingstarted.adoc [Getting Started]-Leitfaden gezeigt wird. Fügen Sie die folgenden Konfigurationen in der Apache Felix Web Console hinzu:"

#. type: Labeled list
#: edge/implement.adoc:387
#, no-wrap
msgid "Controller Debug Log"
msgstr "Controller Debug Log"

#. type: Plain text
#: edge/implement.adoc:389
msgid "ID: `ctrlDebugLog0`"
msgstr "ID: ' ctrlDebugLog0 '"

#. type: Plain text
#: edge/implement.adoc:390 edge/implement.adoc:394 edge/implement.adoc:400 edge/implement.adoc:404
msgid "Enabled: `checked`"
msgstr "Aktiviert: \"geprüft\""

#. type: Labeled list
#: edge/implement.adoc:391
#, no-wrap
msgid "Scheduler All Alphabetically"
msgstr "Geedulter alles Alphabetisch"

#. type: Plain text
#: edge/implement.adoc:393
msgid "ID: `scheduler0`"
msgstr "ID: ' scheduler0 '"

#. type: Plain text
#: edge/implement.adoc:395
msgid "Cycle time: `1000`"
msgstr "Taktzeit: 1000"

#. type: Labeled list
#: edge/implement.adoc:396
#, no-wrap
msgid "Bridge Modbus/TCP"
msgstr "Brücke Modbus/TCP"

#. type: Plain text
#: edge/implement.adoc:398
msgid "ID: `modbus0`"
msgstr "ID: ' modbus0 '"

#. type: Plain text
#: edge/implement.adoc:399
msgid "IP-Address: `localhost`"
msgstr "IP-Adresse: ' localhost '"

#. type: Labeled list
#: edge/implement.adoc:401
#, no-wrap
msgid "Meter Simulated"
msgstr "Meter simuliert"

#. type: Plain text
#: edge/implement.adoc:403
msgid "ID: `meter0`"
msgstr "ID: ' Meter0 '"

#. type: Plain text
#: edge/implement.adoc:405
msgid "Meter-Type: `PRODUCTION`"
msgstr "Metertyp: ' PRODUCTION '"

#. type: Plain text
#: edge/implement.adoc:406
msgid "Modbus-ID: `modbus0`"
msgstr "Modbus-ID: ' modbus0 '"

#. type: Plain text
#: edge/implement.adoc:407
msgid "Modbus Unit-ID: `1`"
msgstr "Modbus Unit-ID: ' 1 '"

#. type: Plain text
#: edge/implement.adoc:409
msgid "In the Eclipse IDE console log you should see an output like this:"
msgstr "Im Eclipse IDE-Konsolenprotokoll sollten Sie eine Ausgabe wie diese sehen:"

#. type: delimited block -
#: edge/implement.adoc:411
#, no-wrap
msgid "2018-11-14 23:03:03,898 [Executor] INFO  [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _sum[Ess SoC:0 %|L:0 W Grid:0 W Production:500 W Consumption L:500 W] meter0[L:500 W]\n"
msgstr "2018-11-14 23:03:03898 [Executor] INFO [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _ sum [Ess SoC:0% | L:0 W Grid:0 W Production:500 W Consumption L:500 W] meter0[L:500 W]\n"

#. type: Plain text
#: edge/implement.adoc:413
msgid "It shows a Production of `500 W` which is what is provided by the simulated meter device. Congrats!"
msgstr "Es zeigt eine Produktion von ' 500 W ', die durch das simulierte Zählergerät zur Verfügung gestellt wird. Congrats!"

#. type: Plain text
#: edge/nature.adoc:13
msgid "Physical hardware is abstracted in OpenEMS Edge using _Natures_. A Nature defines a set of characteristics and attributes which need to be provided by each OpenEMS component that implements it. These characteristics are defined by Channels. For example an implementation of an `Ess` (Energy Storage System), needs to provide an `Soc`-Channel (State of charge of the battery)."
msgstr "Physikalische Hardware wird in OpenEMS Edge mit _ Natures _ abstrahiert. Eine Natur definiert eine Reihe von Eigenschaften und Attributen, die von jeder OpenEMS-Komponente, die sie implementiert, bereitgestellt werden müssen. Diese Eigenschaften werden durch Kanäle definiert. Zum Beispiel muss die Einführung eines \"Ess\" (Energy Storage System) einen ' Soc '-Kanal (Ladezustand der Batterie) bereitstellen."

#. type: Plain text
#: edge/nature.adoc:15
msgid "Technically Natures are implemented as OSGi API Bundles."
msgstr "Technisch werden Natures als OSGi API Bundles implementiert."

#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.battery.api.adoc:3
msgid "A battery that is connected to a battery inverter."
msgstr "Eine Batterie, die an einen Batteriewechselrichter angeschlossen ist."

#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.controller.api.adoc:3
msgid "A Controller; see the Controller chapter for details."
msgstr "Ein Controller; Weitere Informationen finden Sie im Controller-Kapitel."

#. type: Title =
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:1
#, no-wrap
msgid "ESS (Energy Storage System)"
msgstr "Offenes Energiemanagementsystem"

#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:4
msgid "An Energy Storage System is an integrated system with battery and battery inverter."
msgstr "Ein Energiespeicher-System ist ein integriertes System mit Batterie-und Batteriewechselrichter."

#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:5
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/api/SymmetricEss.java[Ess icon:code[]]"
msgstr "link: https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/api/SymmetricEss.java [Ess icon:code[]]"

#. +
#. |===
#. include::https://raw.githubusercontent.com/OpenEMS/openems/develop/doc/_old/devices/_include/EssNature.adoc[tag=channels]
#. |===
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:11
msgid "A generic Energy Storage System"
msgstr "Offenes Energiemanagementsystem"

#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:12
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/api/SymmetricEss.java[SymmetricEssReadonly icon:code[]]"
msgstr "link: https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/api/SymmetricEss.java [SymmetricEssReadonly icon:code[]]"

#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:15
msgid "A symmetric Energy Storage System in readonly-mode."
msgstr "Ein symmetrisches Energiespeicher-System im Lesemodus."

#. link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/api/ManagedSymmetricEss.java[SymmetricEss icon:code[]]::
#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:19
msgid "A symmetric, controllable Energy Storage System."
msgstr "Ein symmetrisches, kontrollierbares Energiespeicher-System."

#. TODO: describe SymmetricPower 'Active/Reactive Power circle' + callback
#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:22
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/dccharger/api/EssDcCharger.java[EssDcCharger icon:code[]]"
msgstr "link: https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.ess.api/src/io/openems/edge/ess/dccharger/api/EssDcCharger.java [EssDcCharger icon:code[]]"

#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.ess.api.adoc:24
msgid "A solar charger that is connected to DC side of an energy storage system."
msgstr "Ein Solarladegerät, das an die Gleichstromseite eines Energiespeichers angeschlossen ist."

#. type: Title =
#: edge/nature.adoc.d/io.openems.edge.evcs.api.adoc:1
#, no-wrap
msgid "EVCS (Electric Vehicle Charging Station)"
msgstr "EVCS (Electric Vehicle Charging Station)"

#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.evcs.api.adoc:3
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.evcs.api/src/io/openems/edge/evcs/api/Evcs.java[Evcs icon:code[]]"
msgstr "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.evcs.api/src/io/openems/edge/evcs/api/Evcs.java [Evcs icon:code[]]"

#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.evcs.api.adoc:5
msgid "A charging station for electric vehicles like e-cars and e-buses."
msgstr "Eine Ladestation für Elektrofahrzeuge wie E-Autos und E-Busse."

#. type: Title =
#: edge/nature.adoc.d/io.openems.edge.io.api.adoc:1
#, no-wrap
msgid "I/O (Digital Input/Output)"
msgstr "I/O (Digital Input/Output)"

#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.io.api.adoc:3
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.io.api/src/io/openems/edge/io/api/DigitalOutput.java[DigitalOutput icon:code[]]"
msgstr "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.io.api/src/io/openems/edge/io/api/DigitalOutput.java [DigitalOutput icon:code[]]"

#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.io.api.adoc:5
msgid "One or more digital outputs or relays."
msgstr "Ein oder mehrere digitale Ausgänge oder Relais."

#. type: Title =
#: edge/nature.adoc.d/io.openems.edge.meter.api.adoc:1
#, no-wrap
msgid "Meter"
msgstr "Meter"

#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.meter.api.adoc:3
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.meter.api/src/io/openems/edge/meter/api/MeterType.java[Meter icon:code[]]"
msgstr "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.meter.api/src/io/openems/edge/meter/api/MeterType.java [Meter icon:code[]]"

#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.meter.api.adoc:6
msgid "A generic electric power meter."
msgstr "Ein generischer Stromzähler."

#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.meter.api.adoc:7
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.meter.api/src/io/openems/edge/meter/api/SymmetricMeter.java[SymmetricMeter icon:code[]]"
msgstr "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.meter.api/src/io/openems/edge/meter/api/SymmetricMeter.java[SymmetricMeter icon:code[]]"

#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.meter.api.adoc:10
msgid "A power meter for symmetric metering."
msgstr "Ein Strommesser für symmetrische Dosierung."

#. type: Labeled list
#: edge/nature.adoc.d/io.openems.edge.meter.api.adoc:11
#, no-wrap
msgid "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.meter.api/src/io/openems/edge/meter/api/AsymmetricMeter.java[AsymmetricMeter icon:code[]]"
msgstr "link:https://github.com/OpenEMS/openems/blob/develop/io.openems.edge.meter.api/src/io/openems/edge/meter/api/AsymmetricMeter.java [AsymmetricMeter icon:code[]]"

#. TODO add channels
#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.meter.api.adoc:13
msgid "A power meter for asymmetric metering."
msgstr "Ein Machtmesser für asymmetrische Dosierung."

#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.scheduler.api.adoc:3
msgid "A Scheduler; see the Scheduler chapter for details."
msgstr "Ein Scheduler; Details dazu finden Sie im Scheduler Kapitel."

#. type: Title =
#: edge/nature.adoc.d/io.openems.edge.timedata.api.adoc:1
#, no-wrap
msgid "Timedata"
msgstr "Timedata"

#. type: Plain text
#: edge/nature.adoc.d/io.openems.edge.timedata.api.adoc:3
msgid "A provider for time-series data, like historic recordings of Channels."
msgstr "Ein Anbieter für Zeitreihendaten, wie historische Aufnahmen von Channels."

#. type: Plain text
#: edge/scheduler.adoc:13
msgid "A OpenEMS Edge Scheduler plans the execution of Controllers. It defines..."
msgstr "Ein OpenEMS Edge Scheduler plant die Ausführung von Controllern. Sie definiert ..."

#. type: Plain text
#: edge/scheduler.adoc:15
msgid "which Controllers are executed"
msgstr "Welche Controller ausgeführt werden"

#. type: Plain text
#: edge/scheduler.adoc:16
msgid "in which priority the Controllers are executed"
msgstr "In welcher Priorität die Kontrolleure ausgeführt werden"

#. TODO
#. == Developing a Scheduler
#. type: Title =
#: edge/scheduler.adoc.d/io.openems.edge.scheduler.allalphabetically.adoc:1
#, no-wrap
msgid "All-Alphabetically"
msgstr "Alles Alphabetisch-AltrAlphabetisch-Ales-Alphabetisch-"

#. type: Plain text
#: edge/scheduler.adoc.d/io.openems.edge.scheduler.allalphabetically.adoc:4
msgid "Takes an ordered list of Component IDs. All remaining Controllers are afterwards ordered alphabetically by their ID."
msgstr "Nimmt eine geordnete Liste der Bauteile auf. Alle verbliebenen Controller werden anschließend alphabetisch nach ihrer ID geordnet."

#. type: Plain text
#: edge/scheduler.adoc.d/io.openems.edge.scheduler.allalphabetically.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.scheduler.allalphabetically[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.scheduler.allalphabetically [Source Code icon: github []]"

#. type: Title =
#: edge/scheduler.adoc.d/io.openems.edge.scheduler.fixedorder.adoc:1
#, no-wrap
msgid "Fixed Order"
msgstr "Feste Bestellung"

#. type: Plain text
#: edge/scheduler.adoc.d/io.openems.edge.scheduler.fixedorder.adoc:4
msgid "Takes a list of Component IDs and returns the Controllers statically sorted by this order."
msgstr "Nimmt eine Liste von Komponentenausweisen auf und gibt die Controller statisch sortiert nach dieser Reihenfolge zurück."

#. type: Plain text
#: edge/scheduler.adoc.d/io.openems.edge.scheduler.fixedorder.adoc:5
msgid "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.scheduler.fixedorder[Source Code icon:github[]]"
msgstr "https://github.com/OpenEMS/openems/tree/develop/io.openems.edge.scheduler.fixedorder [Source Code icon: github []]"

#. type: Title =
#: full-doc.adoc:3
#, no-wrap
msgid "OpenEMS"
msgstr "OpenEMS IoT Stack"

#. * xref:gettingstarted.adoc[Getting Started]
#. * xref:coreconcepts.adoc[Core concepts & terminology]
#. * OpenEMS Edge
#. ** xref:edge/architecture.adoc[Architecture]
#. ** xref:edge/configuration.adoc[Configuration]
#. ** xref:edge/nature.adoc[Nature]
#. ** xref:edge/scheduler.adoc[Scheduler]
#. ** xref:edge/controller.adoc[Controller]
#. ** xref:edge/bridge.adoc[Bridge]
#. ** xref:edge/device_service.adoc[Device & Service]
#. ** xref:edge/implement.adoc[Implementing a Device]
#. ** xref:edge/build.adoc[Build OpenEMS Edge]
#. ** xref:edge/deploy.adoc[Deploy OpenEMS Edge]
#. * OpenEMS UI
#. ** xref:ui/build.adoc[Build OpenEMS UI]
#. * OpenEMS Backend
#. ** xref:backend/architecture.adoc[Architecture]
#. ** xref:backend/configuration.adoc[Configuration]
#. ** xref:backend/backend-to-backend.adoc[Backend-to-Backend]
#. ** xref:backend/build.adoc[Build OpenEMS Backend]
#. ** xref:backend/deploy.adoc[Deploy OpenEMS Backend]
#. * xref:component-communication/index.adoc[Component Communication]
#. * xref:simulation.adoc[Simulation]
#. * xref:documentation.adoc[Documentation]
#. * xref:randd.adoc[Research and Development]
#. * https://openems.github.io/openems.io/javadoc/[Javadoc]
#. type: Title =
#: gettingstarted.adoc:1
#, no-wrap
msgid "Getting Started"
msgstr "Erste Schritte"

#. type: Plain text
#: gettingstarted.adoc:13
msgid "This quick 'Getting Started' should help you setup up a complete development environment. On finishing you will have a working instance of OpenEMS Edge, with simulated energy storage and photovoltaic system, as well as an OpenEMS UI for monitoring the simulator inside your web browser."
msgstr "Diese schnellen 'Ersten Schritte' sollten Ihnen helfen, eine komplette Entwicklungsumgebung einzurichten. Nach der Fertigstellung haben Sie eine funktionierende Instanz von OpenEMS Edge, mit simuliertem Stromspeichersystem und Photovoltaikanlage, sowie eine OpenEMS UI zur Überwachung des Simulators in Ihrem Webbrowser."

#. type: Title ==
#: gettingstarted.adoc:14
#, no-wrap
msgid "Download the source code"
msgstr "Den Quellcode herunterladen"

#. type: Plain text
#: gettingstarted.adoc:17
msgid "Download any https://git-scm.com[git client icon:external-link[]] and install it. Our recommendation is https://www.sourcetreeapp.com/[Sourcetree by Atlassian icon:external-link[]]"
msgstr "Laden Sie alle https:/git-scm.com [git-client icon:external-link[]] herunter und installieren Sie es. Unsere Empfehlung lautet https://www.sourcetreeapp.com/[Sourcetree by Atlassian icon:external-link[]]"

#. type: Plain text
#: gettingstarted.adoc:19
msgid "Clone the OpenEMS git repository"
msgstr "Klone das OpenEMS git-Repository"

#. type: Plain text
#: gettingstarted.adoc:21
msgid "In Sourcetree:"
msgstr "In Sourcetree:"

#. type: Plain text
#: gettingstarted.adoc:28
msgid "... press btn:[File] -> btn:[Clone] ... enter the git repository path `https://github.com/OpenEMS/openems.git` ... select a target directory, for example `C:\\Users\\<USER>\\git\\openems` ... open btn:[Advanced Settings] ... select the branch btn:[develop] ... and press btn:[Clone]."
msgstr "... Drücken tn: [Datei]-> btn: [Clone] ... Geben Sie den git-Projektarchiv-Pfad `https://github.com/OpenEMS/openems.git` ein ... Wählen Sie ein Zielverzeichnis, zum Beispiel `C:\\Users\\<USER>\\git\\` ... klicken Sie btn:[Advanced Settings] ... Wählen Sie den Zweig btn:[develop] ... Und drücken btn:[Clone]."

#. type: Block title
#: gettingstarted.adoc:29
#, no-wrap
msgid "Cloning the git repository using Sourcetree"
msgstr "Das git-Repository mit Sourcetree klonen"

#. type: Target for macro image
#: gettingstarted.adoc:30
#, no-wrap
msgid "sourcetree.png"
msgstr "sourcetree.png"

#. type: Plain text
#: gettingstarted.adoc:33
msgid "Alternatively: with the git command line utility"
msgstr "Alternativ: Mit dem git-Befehlszeilenprogramm"

#. type: Plain text
#: gettingstarted.adoc:37
msgid "... open a console ... change to the target directory ... execute `git clone https://github.com/OpenEMS/openems.git --branch develop`"
msgstr "... Öffnen Sie eine Konsole ... Wechseln Sie in das Zielverzeichnis ... Ausführung `git clone https://github.com/OpenEMS/openems.git --branch develop`"

#. type: Plain text
#: gettingstarted.adoc:39
msgid "Git is downloading the complete source code for you."
msgstr "Git lädt den kompletten Quellcode für Sie herunter."

#. type: Title ==
#: gettingstarted.adoc:40
#, no-wrap
msgid "Setup Eclipse IDE for OpenEMS Edge and Backend"
msgstr "Setup Eclipse IDE für OpenEMS Edge und Backend"

#. type: Plain text
#: gettingstarted.adoc:43
msgid "Prepare Eclipse IDE"
msgstr "Eclipse IDE vorbereiten"

#. type: Plain text
#: gettingstarted.adoc:44
msgid "Download http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html[Java SE Development Kit 8 icon:external-link[]] and install it"
msgstr "Download http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html[Java SE Development Kit 8 icon:external-link[]] und Installation"

#. type: Plain text
#: gettingstarted.adoc:45
msgid "Download https://www.eclipse.org[Eclipse for Java icon:external-link[]], install and start it"
msgstr "Download https://www.eclipse.org [Eclipse for Java icon:external-link[]], installieren und starten"

#. type: Plain text
#: gettingstarted.adoc:47
msgid "On first start you will get asked to create a workspace.  Select a directory - for example `C:\\Users\\<USER>\\git\\openems-workspace` - and press btn:[Lauch]. _The directory needs to be different from your source code directory selected above._"
msgstr "Beim ersten Start werden Sie gebeten, einen Arbeitsraum zu schaffen.  Wählen Sie ein Verzeichnis-zum Beispiel ' C:\\Users\\<USER>\\git\\openems-Workspace '-und drücken Sie btn: [Lauch]. _ Das Verzeichnis muss sich von Ihrem oben ausgewählten Quellcode-Verzeichnis unterscheiden. _"

#. type: Block title
#: gettingstarted.adoc:48
#, no-wrap
msgid "Creating a workspace in Eclipse IDE"
msgstr "Schaffung eines Arbeitsraumes in Eclipse IDE"

#. type: Target for macro image
#: gettingstarted.adoc:49
#, no-wrap
msgid "eclipse-workspace.png"
msgstr "eclipse-workspace.png"

#. type: Plain text
#: gettingstarted.adoc:52
msgid "Install http://bndtools.org[BndTools icon:external-link[]] in Eclipse:"
msgstr "Installieren Sie http:/bndtools.org[BndTools icon:external-link[]] in Eclipse:"

#. type: Plain text
#: gettingstarted.adoc:54
msgid "Menu: btn:[Help] → btn:[Eclipse Marketplace...] → btn:[Find:] → enter btn:[Bndtools] → press btn:[Install]"
msgstr "Menü: btn: [Help] → btn: [Eclipse Marketplace ...] → btn: [Find:] → enter btn: [bndtools] → press btn: [Install]"

#. type: Plain text
#: gettingstarted.adoc:56
msgid "Import OpenEMS component projects (OSGi bundles):"
msgstr "Import von OpenEMS-Komponentenprojekten (OSGi Bundles):"

#. type: Plain text
#: gettingstarted.adoc:58
msgid "Menu: btn:[File] → btn:[Import...] → btn:[Bndtools] → btn:[Existing Bnd Workspace] → Root directory: btn:[Browse...] → select the directory with the source code - for example `C:\\Users\\<USER>\\git\\openems` → btn:[OK] → btn:[Finish] → \"Switch to Bndtools perspective?\" btn:[yes]"
msgstr "Menü: Btn: [File] → btn: [Import ...] → btn: [Bndtools] → btn: [Bestehendes Bnd Workspace] → Root-Verzeichnis: Btn: [Browse ...] → wählen Sie das Verzeichnis mit dem Quellcode-zum Beispiel ' C:\\User\\your.user\\git\\openems ' → btn: [OK] → btn: [Finish] → \"Switch to Bndtools Perspektive? \"Btn: [Ja]"

#. type: Plain text
#: gettingstarted.adoc:60
msgid "Eclipse should have successfully built OpenEMS Edge and Backend, showing no entry in Problems."
msgstr "Eclipse sollte OpenEMS Edge und Backend erfolgreich gebaut haben, was keinen Eintrag in Probleme zeigt."

#. type: Block title
#: gettingstarted.adoc:61
#, no-wrap
msgid "Eclipse IDE showing 'no problems'"
msgstr "Eclipse IDE zeigt \"keine Probleme\""

#. type: Target for macro image
#: gettingstarted.adoc:62
#, no-wrap
msgid "eclipse-no-problems.png"
msgstr "eclipse-no-problems.png"

#. type: Title ==
#: gettingstarted.adoc:64
#, no-wrap
msgid "Run OpenEMS Edge and start Simulator"
msgstr "Führen Sie OpenEMS Edge und starten Sie den Simulator"

#. type: Plain text
#: gettingstarted.adoc:67
msgid "Run OpenEMS Edge"
msgstr "OpenEMS Edge"

#. type: Plain text
#: gettingstarted.adoc:68
msgid "In Eclipse IDE open the project btn:[io.openems.edge.application] and double click on btn:[EdgeApp.run]."
msgstr "In Eclipse IDE öffnen Sie das Projekt btn: [io.openems.edge.application] und doppelklicken Sie auf btn: [EdgeApp.run]."

#. type: Block title
#: gettingstarted.adoc:69
#, no-wrap
msgid "io.openems.edge.application project in Eclipse IDE"
msgstr "io.openems.edge.application Projekt in Eclipse IDE"

#. type: Target for macro image
#: gettingstarted.adoc:70
#, no-wrap
msgid "eclipse-io.openems.edge.application.png"
msgstr "eclipse-io.openems.edge.application.png"

#. type: Plain text
#: gettingstarted.adoc:73
msgid "Click on btn:[Run OSGi] to run OpenEMS Edge. You should see log outputs on the console inside Eclipse IDE."
msgstr "Klicken Sie auf btn: [Run OSGi], um OpenEMS Edge zu starten. Sie sollten die Log-Ausgänge auf der Konsole in Eclipse IDE sehen."

#. type: Block title
#: gettingstarted.adoc:74
#, no-wrap
msgid "OpenEMS Edge initial log output"
msgstr "OpenEMS Edge erste Protokollausgabe"

#. type: Target for macro image
#: gettingstarted.adoc:75
#, no-wrap
msgid "eclipse-edge-initial-log-output.png"
msgstr "eclipse-edge-initial-log-output.png"

#. type: Plain text
#: gettingstarted.adoc:78
msgid "Configure and start the Simulator"
msgstr "Konfiguration und Start des Simulators"

#. type: Plain text
#: gettingstarted.adoc:79
msgid "Open the http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]]."
msgstr "Öffnen Sie das http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]]."

#. type: Plain text
#: gettingstarted.adoc:81 gettingstarted.adoc:258
msgid "Login with username *admin* and password *admin*."
msgstr "Melden Sie sich mit Benutzername * Admin * und Passwort * admin * an."

#. type: Plain text
#: gettingstarted.adoc:83
msgid "Note: *Apache Felix Web Console Configuration* only works with a running console, otherwise you will receive a *error message* on your Browser."
msgstr "Hinweis: * Apache Felix Web Console Configuration * funktioniert nur mit einer laufenden Konsole, sonst erhalten Sie eine * Fehlermeldung * auf Ihrem Browser."

#. type: Plain text
#: gettingstarted.adoc:88
msgid "Configure a Scheduler"
msgstr "Einen Scheduler konfigurieren"

#. TODO: link to Scheduler description
#. type: Plain text
#: gettingstarted.adoc:91
msgid "The Scheduler is responsible for executing the control algorithms (Controllers) and defines the OpenEMS Edge application cycle"
msgstr "Der Scheduler ist für die Ausführung der Steuerungsalgorithmen (Controller) verantwortlich und definiert den OpenEMS Edge Anwendungszyklus"

#. type: Plain text
#: gettingstarted.adoc:93
msgid "... Click on \"Scheduler All Alphabetically\""
msgstr "... Klicken Sie auf \"Scheduler Alles Alphabetisch\""

#. type: Block title
#: gettingstarted.adoc:94
#, no-wrap
msgid "Configuration of All Alphabetically Scheduler"
msgstr "Konfiguration aller Alphabetisch-Planer"

#. type: Target for macro image
#: gettingstarted.adoc:95
#, no-wrap
msgid "config-scheduler-all-alphabetically.png"
msgstr "config-scheduler-all-alphabetically.png"

#. type: Plain text
#: gettingstarted.adoc:98
msgid "... Accept the default values and click btn:[Save]"
msgstr "... Akzeptieren Sie die Standardwerte und klicken Sie auf btn: [Speichern]"

#. type: Plain text
#: gettingstarted.adoc:100
msgid "... You created your first instance of an OpenEMS Component with ID \"scheduler0\". The log shows:"
msgstr "... Sie haben Ihre erste Instanz eines OpenEMS-Komponenten mit ID \"scheduler0\" erstellt. Das Protokoll zeigt:"

#. type: Plain text
#: gettingstarted.adoc:102
msgid "`INFO [onent.AbstractOpenemsComponent] [scheduler0] Activate AllAlphabetically [edge.scheduler.allalphabetically]`"
msgstr "' INFO [Komponente. AbstractOpenemsComponent [scheduler0] Aktivieren AllAlphabetically [edge.scheduler.allalphabetically] '"

#. type: Plain text
#: gettingstarted.adoc:104
msgid "Add any other OpenEMS Components in the same way:"
msgstr "Fügen Sie alle anderen OpenEMS-Komponenten auf die gleiche Weise hinzu:"

#. type: Plain text
#: gettingstarted.adoc:106
msgid "Configure debug outputs on the console: \"Controller Debug Log\". The default values can be accepted without changes."
msgstr "Konfigurieren Sie Debug-Ausgänge auf der Konsole: \"Controller Debug Log\". Die Standardwerte können ohne Änderungen akzeptiert werden."

#. type: Block title
#: gettingstarted.adoc:107
#, no-wrap
msgid "Configuration of Controller Debug Log"
msgstr "Konfiguration des Controllers Debug Log"

#. type: Target for macro image
#: gettingstarted.adoc:108
#, no-wrap
msgid "config-controller-debug-log.png"
msgstr "config-controller-debug-log.png"

#. type: Plain text
#: gettingstarted.adoc:111 gettingstarted.adoc:126 gettingstarted.adoc:155 gettingstarted.adoc:166 gettingstarted.adoc:177 gettingstarted.adoc:214
msgid "The log shows:"
msgstr "Das Protokoll zeigt:"

#. type: Plain text
#: gettingstarted.adoc:113
msgid "`INFO [onent.AbstractOpenemsComponent] [ctrlDebugLog0] Activate DebugLog [edge.controller.debuglog]`,"
msgstr "' INFO [Komponente. AbstractOpenemsComponent] [ctrlDebugLog0] Activate DebugLog [edge.controller.debuglog] ',"

#. type: Plain text
#: gettingstarted.adoc:115
msgid "followed once per second by"
msgstr "Einmal pro Sekunde gefolgt"

#. type: Plain text
#: gettingstarted.adoc:117
msgid "`INFO [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _sum[Ess SoC:0 %|L:0 W Grid L:0 W Production L:0 W Consumption L:0 W]`."
msgstr "' INFO [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _ sum [Ess SoC:0% | L:0 W Grid L:0 W Production L:0 W-Verbrauch L:0 W] '."

#. type: Plain text
#: gettingstarted.adoc:119
msgid "It is once per second because you accepted the default value of \"1000 ms\" for \"Cycle time\" in the Scheduler configuration."
msgstr "Es ist einmal pro Sekunde, weil Sie den Standardwert von \"1000 ms\" für \"Zykluszeit\" in der Scheduler-Konfiguration akzeptiert haben."

#. type: Plain text
#: gettingstarted.adoc:121
msgid "Configure the standard-load-profile datasource using the according input file in the csv-reader: \"Simulator DataSource: CSVReader\". The default values can be accepted without changes. The \"Source\" value is already set to the right input file."
msgstr "Konfigurieren Sie die Standard-Load-Profil-Datasource mit der entsprechenden Eingabedatei im csv-Reader: \"Simulator DataSource: CSVReader\". Die Standardwerte können ohne Änderungen akzeptiert werden. Der \"Source\"-Wert ist bereits auf die richtige Eingabedatei eingestellt."

#. type: Block title
#: gettingstarted.adoc:122
#, no-wrap
msgid "Configuration of Simulator DataSource: CSVReader as standard load profile datasource"
msgstr "Konfiguration von Simulator DataSource: CSVReader als Standard-Lastprofil Datenquelle"

#. type: Target for macro image
#: gettingstarted.adoc:123
#, no-wrap
msgid "config-simulator-datasource-standard-load-profile.png"
msgstr "config-simulator-datasource-standard-load-profile.png"

#. type: Plain text
#: gettingstarted.adoc:128
msgid "`INFO [onent.AbstractOpenemsComponent] [datasource0] Activate CSVDatasource [edge.simulator.datasource.csv]`,"
msgstr "' INFO [Komponente. AbstractOpenemsComponent [datasource0] Aktivieren Sie CSVDatasource [edge.simulator.datasource.csv] ',"

#. type: Plain text
#: gettingstarted.adoc:130
msgid "The data source was configured with the OpenEMS Component ID \"datasource0\" which will be used in the next step as reference."
msgstr "Die Datenquelle wurde mit der OpenEMS Component ID \"datasource0\" konfiguriert, die im nächsten Schritt als Referenz verwendet wird."

#. type: Plain text
#: gettingstarted.adoc:132
msgid "Configure a simulated grid meter: \"Simulator GridMeter Acting\". Configure the Datasource-ID \"datasource0\" to refer to the data source configured above."
msgstr "Konfigurieren Sie einen simulierten Gittermesser: \"Simulator GridMeter Acting\". Konfigurieren Sie die Datasource-ID \"datasource0\", um auf die oben konfigurierte Datenquelle zu verweisen."

#. type: Block title
#: gettingstarted.adoc:133
#, no-wrap
msgid "Configuration of Simulator GridMeter Acting"
msgstr "Konfiguration von Simulator-GridMeter-Acting"

#. type: Target for macro image
#: gettingstarted.adoc:134
#, no-wrap
msgid "config-simulator-grid-meter-acting.png"
msgstr "config-simulator-grid-meter-acting.png"

#. type: Plain text
#: gettingstarted.adoc:137
msgid "This time some more logs will show up. Most importantly they show, that the Grid meter now shows a power value."
msgstr "Dieses Mal werden noch einige Protokolle erscheinen. Am wichtigsten ist, dass sie zeigen, dass der Grid-Zähler nun einen Stromwert zeigt."

#. type: delimited block -
#: gettingstarted.adoc:143
#, no-wrap
msgid ""
"INFO  [onent.AbstractOpenemsComponent] [meter0] Activate GridMeter [edge.simulator.meter.grid.acting]\n"
"[onent.AbstractOpenemsComponent] [meter0] Deactivate GridMeter [edge.simulator.meter.grid.acting]\n"
"[onent.AbstractOpenemsComponent] [meter0] Activate GridMeter [edge.simulator.meter.grid.acting]\n"
"[e.controller.debuglog.DebugLog] [ctrlDebugLog0] _sum[Ess SoC:0 %|L:0 W Grid L:1423 W Production L:0 W Consumption L:1423 W] meter0[1423 W]\n"
msgstr ""
"INFO [Komponente. AbstractOpenemsComponent [meter0] Aktivieren GridMeter [edge.simulator.meter.grid.acting]\n"
"[Komponente. AbstractOpenemsComponent] [meter0] Deaktivieren GridMeter [edge.simulator.meter.grid.acting]\n"
"[Komponente. AbstractOpenemsComponent [meter0] Aktivieren GridMeter [edge.simulator.meter.grid.acting]\n"
"[e.controller.debuglog.DebugLog] [ctrlDebugLog0] _ sum [Ess SoC:0% | L:0 W Grid L:1423 W Production L:0 W Verbrauch L:1423 W] meter0[1423 W]\n"

#. type: Plain text
#: gettingstarted.adoc:146
msgid "This setup causes the simulated grid-meter to take the standardized load-profiles data as input parameter."
msgstr "Diese Einstellung führt dazu, dass der simulierte Gittermesser die standardisierten Belastungsprofile als Eingangsparameter nimmt."

#. type: Plain text
#: gettingstarted.adoc:148
msgid "'Acting' referrs to the fact, that this meter actively provides data - in opposite to a 'Reacting' device that is reacting on other components: for example the 'Simulator.EssSymmetric.Reacting' configured below."
msgstr "\"Acting\" verweist darauf, dass dieser Zähler aktiv Daten zur Verfügung stellt-im Gegensatz zu einem \"Reacting\"-Gerät, das auf andere Komponenten reagiert: Zum Beispiel die \"Simulator.EssSymmetric.Reacting\", die unten konfiguriert ist."

#. type: Plain text
#: gettingstarted.adoc:150
msgid "Configure a simulated reacting energy storage system: \"Simulator EssSymmetric Reacting\". The default values can be accepted without changes. (If you choose an other setup as the one described here you may have to create a new Datasource-Component and provide its ID here. The actual data is ignored, but the Datasource's Time-Delta value is required to calculate values with time-dependant units.)"
msgstr ""
"Konfigurieren Sie ein simuliertes Reaktionssystem: \"Simulator EssSymmetrische Reaktion\". Die Standardwerte können ohne Änderungen akzeptiert werden. (Wenn Sie ein anderes Setup wählen, wie es hier beschrieben wird, müssen Sie unter Umständen eine neue Datenbankkomponente erstellen und hier ihre ID angeben. Die tatsächlichen Daten werden ignoriert, aber der Time-Delta-Wert des Datasource ist erforderlich, um Werte mit "
"zeitabhängigen Einheiten zu berechnen.)"

#. type: Block title
#: gettingstarted.adoc:151
#, no-wrap
msgid "Configuration of Simulator EssSymmetric Reacting"
msgstr "Konfiguration des Simulators EssSymmetric reagiert"

#. type: Target for macro image
#: gettingstarted.adoc:152
#, no-wrap
msgid "config-simulator-esssymmetric-reacting.png"
msgstr "config-simulator-esssymmetric-reacting.png"

#. type: Plain text
#: gettingstarted.adoc:157
msgid "`INFO [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _sum[Ess SoC:50 %|L:0 W Grid L:864 W Production L:0 W Consumption L:864 W] ess0[SoC:50 %|L:0 W|OnGrid] meter0[864 W]`"
msgstr "' INFO [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _ sum [Ess SoC:50% | L:0 W Grid L:864 W Production L:0 W Verbrauch L:864 W] ess0[SoC:50% | L:0 W | OnGrid] meter0[864 W] '"

#. type: Plain text
#: gettingstarted.adoc:159
msgid "Note, that the DebugLog now shows data for the battery, but the charge/discharge power stays at \"0 W\" and the state of charge stays at \"50 %\" as configured. Next step is to configure a control algorithm that tells the battery to charge or discharge."
msgstr "Beachten Sie, dass das DebugLog nun Daten für die Batterie anzeigt, aber die Lade-/Entladeleistung bei \"0 W\" bleibt und der Ladezustand bei \"50%\" bleibt, wie konfiguriert. Der nächste Schritt ist, einen Steueralgorithmus zu konfigurieren, der der Batterie sagt, dass sie aufladen oder entladen soll."

#. type: Plain text
#: gettingstarted.adoc:161
msgid "Configure the self-consumption optimization algorithm: \"Controller Balancing Symmetric\". Configure the Ess-ID \"ess0\" and Grid-Meter-ID \"meter0\" to refer to the components configured above."
msgstr "Konfigurieren Sie den Algorithmus zur Selbstverbrauchsoptimierung: \"Controller Balancing Symmetric\". Konfigurieren Sie die Ess-ID \"ess0\" und die Grid-Meter-ID \"meter0\", um auf die oben konfigurierten Komponenten zu verweisen."

#. type: Block title
#: gettingstarted.adoc:162
#, no-wrap
msgid "Configuration of Symmetric Balancing Controller"
msgstr "Konfiguration von symmetrischem Balancing Controller"

#. type: Target for macro image
#: gettingstarted.adoc:163
#, no-wrap
msgid "config-controller-balancing-symmetric.png"
msgstr "config-controller-balancing-symmetric.png"

#. type: Plain text
#: gettingstarted.adoc:168
msgid "`INFO [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _sum[Ess SoC:49 %|L:1167 W Grid L:-39 W Production L:0 W Consumption L:1128 W] ess0[SoC:49 %|L:1167 W|OnGrid] meter0[-39 W]`"
msgstr "' INFO [e.controller.debuglog.DebugLog] [ctrlDebugLog0] _ sum [Ess SoC:49% | L:1167 W Grid L:-39 W Production L:0 W Consumption L:1128 W] ess0[SoC:49% | L:1167 W | OnGrid] meter0[-39 W] '"

#. type: Plain text
#: gettingstarted.adoc:170
msgid "Note, how the Controller now tells the battery to discharge (`Ess SoC:49 %|L:1167 W`), trying to balance the Grid power to \"0 W\" (`Grid L:-39 W`):"
msgstr "Beachten Sie, wie der Controller nun dem Akku sagt, dass er entladen soll (' Ess SoC:49% | L:49 W ') und versucht, die Grid-Power auf \"0 W\" (' Grid L:-39 W ') auszugleichen:"

#. type: Plain text
#: gettingstarted.adoc:172
msgid "Configure the websocket Api Controller: \"Controller Api Websocket\". The default values can be accepted without changes."
msgstr "Konfigurieren Sie den Websocket Api Controller: \"Controller Api Websocket\". Die Standardwerte können ohne Änderungen akzeptiert werden."

#. type: Block title
#: gettingstarted.adoc:173
#, no-wrap
msgid "Configuration of Controller Api Websocket"
msgstr "Konfiguration des Controllers Api Websocket"

#. type: Target for macro image
#: gettingstarted.adoc:174
#, no-wrap
msgid "config-controller-api-websocket.png"
msgstr "config-controller-api-websocket.png"

#. type: delimited block -
#: gettingstarted.adoc:181
#, no-wrap
msgid ""
"INFO  [onent.AbstractOpenemsComponent] [ctrlApiWebsocket0] Activate WebsocketApi [edge.controller.api.websocket]\n"
"INFO  [ler.api.websocket.WebsocketApi] [ctrlApiWebsocket0] Websocket-Api started on port [8085].\n"
msgstr ""
"INFO [Komponente. AbstractOpenemsComponent] [ctrlApiWebsocket0] Aktivieren WebsocketApi [edge.controller.api.websocket]\n"
"INFO [ler.api.websocket.WebsocketApi] [ctrlApiWebsocket0] Websocket-Api startete auf Port [8085].\n"

#. type: Plain text
#: gettingstarted.adoc:184
msgid "The Controller Api Websocket is required to enable access to OpenEMS Edge by a local OpenEMS UI."
msgstr "Der Controller Api Websocket ist erforderlich, um den Zugriff auf OpenEMS Edge durch eine lokale OpenEMS-UI zu ermöglichen."

#. type: Title ==
#: gettingstarted.adoc:185
#, no-wrap
msgid "Setup Visual Studio Code for OpenEMS UI"
msgstr "Setup Visual Studio Code für OpenEMS UI"

#. type: Plain text
#: gettingstarted.adoc:188
msgid "Download https://nodejs.org[node.js LTS icon:external-link[]] and install it."
msgstr "Download https:/nodejs.org[node.js LTS icon:external-link[]] und installieren Sie es."

#. type: Plain text
#: gettingstarted.adoc:189
msgid "Download https://code.visualstudio.com/[Visual Studio Code icon:external-link[]], install and start it."
msgstr "Laden Sie https://code.visualstudio.com/[Visual Studio Code icon:external-link[]], installieren Sie es und starten Sie es."

#. type: Plain text
#: gettingstarted.adoc:190
msgid "Open OpenEMS UI source code in Visual Studio Code:"
msgstr "Öffnen Sie OpenEMS UI Quellcode in Visual Studio Code:"

#. type: Plain text
#: gettingstarted.adoc:192
msgid "Menu: btn:[File] → btn:[Open Folder...] → Select the `ui` directory inside the downloaded source code (for example `C:\\Users\\<USER>\\git\\openems\\ui`) → btn:[Select directory]"
msgstr "Menü: btn: [Datei] → btn: [Offener Ordner ...] → Wählen Sie das ' ui '-Verzeichnis innerhalb des heruntergeladenen Quellcodes (zum Beispiel ' C:\\Users\\<USER>\\git\\openems\\ui ') → btn: [Verzeichnis auswählen]"

#. type: Plain text
#: gettingstarted.adoc:194
msgid "Open the integrated terminal:"
msgstr "Offenes Energiemanagementsystem"

#. type: Plain text
#: gettingstarted.adoc:196
msgid "Menu: btn:[Terminal] → btn:[New Terminal]"
msgstr "Menü: btn: [Terminal] → btn: [Neues Terminal]"

#. type: Plain text
#: gettingstarted.adoc:198
msgid "Install https://cli.angular.io/[Angular CLI icon:external-link[]]:"
msgstr "Install https://cli.angular.io/[Angular CLI icon:external-link[]]:"

#. type: Plain text
#: gettingstarted.adoc:200
msgid "`npm install -g @angular/cli`"
msgstr "' npm install-g @angular/cli '"

#. type: Plain text
#: gettingstarted.adoc:202
msgid "If you receive an error message that the command `npm` could not be found, make sure that node.js is installed and restart Visual Studio Code."
msgstr "Wenn Sie eine Fehlermeldung erhalten, dass der Befehl ' npm ' nicht gefunden werden konnte, stellen Sie sicher, dass node.js installiert ist und starten Sie den Visual Studio Code neu."

#. type: Plain text
#: gettingstarted.adoc:204
msgid "Resolve and download dependencies:"
msgstr "Abhängigkeiten lösen und herunterladen:"

#. type: Plain text
#: gettingstarted.adoc:206
msgid "`npm install`"
msgstr "\"Npm install\""

#. type: Title ==
#: gettingstarted.adoc:207
#, no-wrap
msgid "Run OpenEMS UI"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche"

#. type: Plain text
#: gettingstarted.adoc:210 gettingstarted.adoc:345
msgid "In Visual Studios integrated terminal type..."
msgstr "In Visual Studios integrierter Terminaltyp ..."

#. type: Plain text
#: gettingstarted.adoc:212 gettingstarted.adoc:347
msgid "`ng serve`"
msgstr "\"Ng serve\""

#. type: Plain text
#: gettingstarted.adoc:216
msgid "`NG Live Development Server is listening on localhost:4200, open your browser on http://localhost:4200/`"
msgstr "' NG Live Development Server ist das Hören auf localhost:4200, öffnen Sie Ihren Browser auf http://localhost:4200/'"

#. type: Plain text
#: gettingstarted.adoc:218 gettingstarted.adoc:349
msgid "Open a browser at http://localhost:4200"
msgstr "Öffnen Sie einen Browser bei http://localhost:4200"

#. type: Plain text
#: gettingstarted.adoc:220
msgid "You should see OpenEMS UI. Log in as user \"guest\" by clicking on the tick mark. Alternatively type \"admin\" in the password field to log in with extended permissions."
msgstr "Sie sollten OpenEMS UI sehen. Melden Sie sich als Benutzer \"Gast\" an, indem Sie auf das Häkchen klicken. Alternativ tippen Sie \"Admin\" in das Passwortfeld, um sich mit erweiterten Berechtigungen einzuloggen."

#. type: Block title
#: gettingstarted.adoc:221
#, no-wrap
msgid "OpenEMS UI Login screen"
msgstr "OpenEMS UI Screenshots"

#. type: Target for macro image
#: gettingstarted.adoc:222
#, no-wrap
msgid "openems-ui-login.png"
msgstr "openems-ui-login.png"

#. type: Plain text
#: gettingstarted.adoc:225
msgid "Change to the Energymonitor by clicking on the arrow."
msgstr "Wechseln Sie auf den Energymonitor, indem Sie auf den Pfeil klicken."

#. type: Block title
#: gettingstarted.adoc:226
#, no-wrap
msgid "OpenEMS UI Overview screen"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche"

#. type: Target for macro image
#: gettingstarted.adoc:227
#, no-wrap
msgid "openems-ui-overview.png"
msgstr "openems-ui-overview.png"

#. type: Plain text
#: gettingstarted.adoc:230
msgid "You should see the Energymonitor showing the same data as the DebugLog output on the console."
msgstr "Sie sollten den Energymonitor sehen, der die gleichen Daten wie die DebugLog-Ausgabe auf der Konsole zeigt."

#. type: Block title
#: gettingstarted.adoc:231
#, no-wrap
msgid "OpenEMS UI Energymonitor screen"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche"

#. type: Target for macro image
#: gettingstarted.adoc:232
#, no-wrap
msgid "openems-ui-edge-overview.png"
msgstr "openems-ui-edge-overview.png"

#. type: Plain text
#: gettingstarted.adoc:235
msgid "OpenEMS UI will complain that \"no timedata source is available\". Because of this the historic chart is not yet functional."
msgstr "OpenEMS UI wird sich darüber beschweren, dass \"keine timedata-Quelle verfügbar ist\". Aus diesem Grund ist die historische Grafik noch nicht funktionsfähig."

#. type: Title ==
#: gettingstarted.adoc:236
#, no-wrap
msgid "Integrate OpenEMS Backend"
msgstr "OpenEMS-Backend integrieren"

#. type: Plain text
#: gettingstarted.adoc:239
msgid "Instead of having Edge and UI talk to each other directly, the communication can also be proxied via Backend."
msgstr "Anstatt Edge und UI direkt miteinander sprechen zu lassen, kann die Kommunikation auch über Backend proxiert werden."

#. type: Title ===
#: gettingstarted.adoc:240
#, no-wrap
msgid "Run and configure OpenEMS Backend"
msgstr "OpenEMS Backend starten und konfigurieren"

#. type: Plain text
#: gettingstarted.adoc:243
msgid "In Eclipse IDE open the project btn:[io.openems.backend.application] and double click on btn:[BackendApp.bndrun]."
msgstr "In Eclipse IDE öffnen Sie das Projekt btn: [io.openems.backend.application] und doppelklicken Sie auf btn: [BackendApp.bndrun]."

#. type: Block title
#: gettingstarted.adoc:244
#, no-wrap
msgid "io.openems.backend.application project in Eclipse IDE"
msgstr "io.openems.backend.application Projekt in Eclipse IDE"

#. type: Target for macro image
#: gettingstarted.adoc:245
#, no-wrap
msgid "eclipse-io.openems.backend.application.png"
msgstr "eclipse-io.openems.backend.application.png"

#. type: Plain text
#: gettingstarted.adoc:248
msgid "Click on btn:[Run OSGi] to run OpenEMS Backend. You should see log outputs on the console inside Eclipse IDE."
msgstr "Klicken Sie auf btn: [Run OSGi], um OpenEMS Backend zu starten. Sie sollten die Log-Ausgänge auf der Konsole in Eclipse IDE sehen."

#. type: Block title
#: gettingstarted.adoc:249
#, no-wrap
msgid "OpenEMS Backend initial log output"
msgstr "OpenEMS Backend erste Log-Ausgabe"

#. type: Target for macro image
#: gettingstarted.adoc:250
#, no-wrap
msgid "eclipse-backend-initial-log-output.png"
msgstr "eclipse-backend-initial-log-output.png"

#. type: Plain text
#: gettingstarted.adoc:253
msgid "Configure the Backend"
msgstr "Den Backend konfigurieren"

#. type: Plain text
#: gettingstarted.adoc:254
msgid "Open the http://localhost:8079/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]]."
msgstr "Öffnen Sie das http://localhost:8079/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]]."

#. type: Plain text
#: gettingstarted.adoc:256
msgid "Apache Felix Web Console for OpenEMS Backend is started on port 8079 by default. This is configured using the `org.osgi.service.http.port` setting in BackendApp.bndrun."
msgstr "Die Apache Felix Web-Konsole für OpenEMS-Backend wird standardmäßig auf Port 8079 gestartet. Dies wird mit der ' org.osgi.service.http.port '-Einstellung in BackendApp.bndrun konfiguriert."

#. type: Block title
#: gettingstarted.adoc:259
#, no-wrap
msgid "Apache Felix Web Console Configuration for OpenEMS Backend"
msgstr "Apache Felix Web-Konsolen-Konfiguration für OpenEMS-Backend"

#. type: Target for macro image
#: gettingstarted.adoc:260
#, no-wrap
msgid "apache-felix-console-backend-configuration.png"
msgstr "apache-felix-console-backend-configuration.png"

#. type: Plain text
#: gettingstarted.adoc:263
msgid "Configure Edge.Websocket"
msgstr "Edge.Websocket konfigurieren"

#. type: Plain text
#: gettingstarted.adoc:265
msgid "The *Edge.Websocket* service is responsible for the communication between OpenEMS Backend and OpenEMS Edge."
msgstr "Der * Edge.Websocket *-Service ist für die Kommunikation zwischen OpenEMS Backend und OpenEMS Edge verantwortlich."

#. type: Plain text
#: gettingstarted.adoc:267
msgid "In the example we are configuring the Port 8081. This port needs to match with what we configure later in OpenEMS Edge."
msgstr "In dem Beispiel konfigurieren wir den Port 8081. Dieser Port muss mit dem übereinstimmen, was wir später in OpenEMS Edge konfigurieren."

#. type: Block title
#: gettingstarted.adoc:268
#, no-wrap
msgid "Configuration of Backend Edge.Websocket"
msgstr "Konfiguration von Backend Edge.Websocket"

#. type: Target for macro image
#: gettingstarted.adoc:269
#, no-wrap
msgid "config-backend-edge.websocket.png"
msgstr "config-backend-edge.websocket.png"

#. type: Plain text
#: gettingstarted.adoc:272
msgid "Configure Ui.Websocket"
msgstr "Ui.Websocket konfigurieren"

#. type: Plain text
#: gettingstarted.adoc:274
msgid "The *Ui.Websocket* service is responsible for the communication between OpenEMS Backend and OpenEMS UI."
msgstr "Der * Ui.Websocket *-Service ist für die Kommunikation zwischen OpenEMS Backend und OpenEMS UI verantwortlich."

#. type: Plain text
#: gettingstarted.adoc:276
msgid "In the example we are configuring the Port 8082. This port needs to match with what we configure later in the OpenEMS UI environment file."
msgstr "In dem Beispiel konfigurieren wir den Port 8082. Dieser Port muss mit dem übereinstimmen, was wir später in der OpenEMS-UI-Umgebungsdatei konfigurieren."

#. type: Block title
#: gettingstarted.adoc:277
#, no-wrap
msgid "Configuration of Backend Ui.Websocket"
msgstr "Konfiguration von Backend Ui.Websocket"

#. type: Target for macro image
#: gettingstarted.adoc:278
#, no-wrap
msgid "config-backend-ui.websocket.png"
msgstr "config-backend-ui.websocket.png"

#. type: Plain text
#: gettingstarted.adoc:281
msgid "Configure Timedata"
msgstr "Timedata konfigurieren"

#. type: Plain text
#: gettingstarted.adoc:283
msgid "The *Timedata* service provider is responsible for holding the current and historic data of each connected Edge device."
msgstr "Der * Timedata *-Dienstleister ist für die Speicherung der aktuellen und historischen Daten jedes angeschlossenen Edge-Gerätes verantwortlich."

#. type: Plain text
#: gettingstarted.adoc:285
msgid "In the example we are configuring the *Timedata.Dummy* service. It takes no configuration parameters, so just press btn:[Save]. In a production system you would want to use a real implementation like *Timedata.InfluxDB*."
msgstr "Im Beispiel konfigurieren wir den * Timedata.Dummy * Service. Es braucht keine Konfigurationsparameter, also drücken Sie einfach btn: [Speichern]. In einem Produktionssystem möchten Sie eine echte Implementierung wie * Timedata.InfluxDB * verwenden."

#. type: Block title
#: gettingstarted.adoc:286
#, no-wrap
msgid "Configuration of Backend Timedata.Dummy"
msgstr "Konfiguration Backend Timedata.Dummy"

#. type: Target for macro image
#: gettingstarted.adoc:287
#, no-wrap
msgid "config-backend-timedata.dummy.png"
msgstr "config-backend-timedata.dummy.png"

#. type: Plain text
#: gettingstarted.adoc:290
msgid "Configure Metadata"
msgstr "Metadaten konfigurieren"

#. type: Plain text
#: gettingstarted.adoc:292
msgid "The *Metadata* service provider is responsible for authentication of Edge devices and Users connecting via UI."
msgstr "Der * Metadata *-Dienstleister ist für die Authentifizierung von Edge-Geräten und Nutzern verantwortlich, die über UI verbunden sind."

#. type: Plain text
#: gettingstarted.adoc:294
msgid "In the example we are configuring the *Metadata.Dummy* service. It takes no configuration parameters, so just press btn:[Save]. In a production system you would want to use a real implementation like *Metadata.File* or *Metadata.Odoo*."
msgstr "Im Beispiel konfigurieren wir den * Metadata.Dummy *-Dienst. Es braucht keine Konfigurationsparameter, also drücken Sie einfach btn: [Speichern]. In einem Produktionssystem möchten Sie eine echte Implementierung wie * Metadata.File * oder * Metadata.Odoo * verwenden."

#. type: Block title
#: gettingstarted.adoc:295
#, no-wrap
msgid "Configuration of Backend Metadata.Dummy"
msgstr "Konfiguration von Backend Metadata.Dummy"

#. type: Target for macro image
#: gettingstarted.adoc:296
#, no-wrap
msgid "config-backend-metadata.dummy.png"
msgstr "config-backend-metadata.dummy.png"

#. type: Title ===
#: gettingstarted.adoc:298
#, no-wrap
msgid "Configure OpenEMS Edge"
msgstr "OpenEMS Edge"

#. type: Plain text
#: gettingstarted.adoc:301
msgid "Next we will configure OpenEMS Edge to connect to the OpenEMS Backend `Edge.Websocket` service."
msgstr "Als nächstes werden wir OpenEMS Edge konfigurieren, um uns mit dem OpenEMS-Backend ' Edge.Websocket ' zu verbinden."

#. type: Plain text
#: gettingstarted.adoc:303
msgid "Switch back to the http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration for OpenEMS Edge icon:external-link[]]."
msgstr "Wechseln Sie zurück zum http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration für OpenEMS Edge icon:external-link[]]."

#. type: Plain text
#: gettingstarted.adoc:305
msgid "Configure the \"Controller Api Backend\" Component. The default values can be accepted without changes right now."
msgstr "Konfigurieren Sie die Komponente \"Controller Api Backend\". Die Standardwerte können jetzt ohne Änderungen akzeptiert werden."

#. type: Block title
#: gettingstarted.adoc:306
#, no-wrap
msgid "Configuration of Controller Api Backend"
msgstr "Konfiguration von Controller Api Backend"

#. type: Target for macro image
#: gettingstarted.adoc:307
#, no-wrap
msgid "config-controller-api-backend.png"
msgstr "config-controller-api-backend.png"

#. type: Plain text
#: gettingstarted.adoc:310
msgid "Some configuration parameters are still noteworthy here:"
msgstr "Einige Konfigurationsparameter sind hier noch bemerkenswert:"

#. type: Plain text
#: gettingstarted.adoc:312
msgid "\"Apikey\" is used to authenticate this Edge at the Backend Metadata service."
msgstr "\"Apikey\" wird verwendet, um diesen Edge im Backend Metadata-Dienst zu authentifizieren."

#. type: Plain text
#: gettingstarted.adoc:313
msgid "\"Uri\" is set to `ws://localhost:8081`. This defines an unencrypted websocket (\"ws://\") connection to the local computer on port \"8081\" like we configured before for the Edge.Websocket."
msgstr "\"Uri\" ist auf ' ws://localhost:8081 ' gesetzt. Damit wird eine unverschlüsselte Websocket (\"ws://\")-Verbindung zum lokalen Computer auf Port \"8081\" definiert, wie wir sie zuvor für den Edge.Websocket konfiguriert haben."

#. type: Plain text
#: gettingstarted.adoc:314
msgid "\"Cycle Time\" defines how often data is sent to Backend"
msgstr "\"Cycle Time\" legt fest, wie oft Daten an Backend gesendet werden"

#. type: Plain text
#: gettingstarted.adoc:316
msgid "Once you press btn:[save] you should see logs in OpenEMS Backend"
msgstr "Sobald Sie btn gedrückt haben: [Speichern], sollten Sie Protokolle in OpenEMS Backend sehen"

#. type: Plain text
#: gettingstarted.adoc:318
msgid "`[ctrlBackend0] Connected to OpenEMS Backend`"
msgstr "' [ctrlBackend0] Connected to OpenEMS Backend '"

#. type: Plain text
#: gettingstarted.adoc:320
msgid "and OpenEMS Edge"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte"

#. type: Plain text
#: gettingstarted.adoc:322
msgid "`[Edge.Websocket] Edge [edge0] connected`"
msgstr "' [Edge.Websocket] Edge [edge0] connected '"

#. type: Title ===
#: gettingstarted.adoc:323
#, no-wrap
msgid "Connect OpenEMS UI with Backend"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche"

#. type: Plain text
#: gettingstarted.adoc:326
msgid "In Visual Studio Code open the file `ui/src/environments/environment.ts` and configure it as follows:"
msgstr "In Visual Studio Code öffnen Sie die Datei ' ui/src/environmentess.ts ' und konfigurieren Sie sie wie folgt:"

#. type: delimited block -
#: gettingstarted.adoc:330
#, no-wrap
msgid "import { Environment } from \"../app/shared/type/environment\";\n"
msgstr "Importieren {Environment} aus \".. /app/shared/type/environment \";\n"

#. type: delimited block -
#: gettingstarted.adoc:337
#, no-wrap
msgid ""
"export const environment: Environment = {\n"
"  production: false,\n"
"  debugMode: true,\n"
"  url: \"ws://localhost:8082\",\n"
"  backend: \"OpenEMS Backend\",\n"
"};\n"
msgstr ""
"Export-Konst-Umgebung: Umwelt = {\n"
"  Produktion: Falsch,\n"
"  DebugMode: Stimmt,\n"
"  URL: \"ws://localhost:8082\",\n"
"  Backend: \"OpenEMS Backend\",\n"
"};\n"

#. type: Plain text
#: gettingstarted.adoc:340
msgid "It is again noteworthy here, that:"
msgstr "Bemerkenswert ist hier wieder:"

#. type: Plain text
#: gettingstarted.adoc:342
msgid "\"url\" is set to `ws://localhost:8082`. This defines an unencrypted websocket (\"ws://\") connection to the local computer on port \"8082\" like we configured before for the Ui.Websocket."
msgstr "\"URL\" ist auf ' ws://localhost:8082 ' gesetzt. Dies definiert eine unverschlüsselte Websocket (\"ws://\") Verbindung zum lokalen Computer auf dem Port \"8082\", wie wir sie zuvor für den Ui.Websocket konfiguriert haben."

#. type: Plain text
#: gettingstarted.adoc:343
msgid "\"backend\" is set to \"OpenEMS Backend\". This option is used in certain places inside OpenEMS UI that need to be treated differently for connections to OpenEMS Edge and OpenEMS Backend."
msgstr "\"Backend\" wird auf \"OpenEMS Backend\" gesetzt. Diese Option wird an bestimmten Stellen innerhalb von OpenEMS-UI verwendet, die für Verbindungen zu OpenEMS Edge und OpenEMS Backend unterschiedlich behandelt werden müssen."

#. type: Plain text
#: gettingstarted.adoc:351
msgid "You should see again OpenEMS UI, but this time you are not asked for a login (because Metadata.Dummy does not require any) and are directly forwarded to the Energy Monitor at `http://localhost:4200/device/edge0/index`. You are now seeing the data from OpenEMS Edge via OpenEMS Backend."
msgstr "Sie sollten wieder OpenEMS UI sehen, aber dieses Mal werden Sie nicht um ein Login gebeten (weil Metadata.Dummy keine benötigt) und werden direkt an den Energy Monitor bei ' http://localhost:4200/device/edge0/index ' weitergeleitet. Sie sehen jetzt die Daten von OpenEMS Edge über OpenEMS Backend."

#. type: Block title
#: gettingstarted.adoc:352
#, no-wrap
msgid "UI via Backend"
msgstr "UI via Backend"

#. type: Target for macro image
#: gettingstarted.adoc:353
#, no-wrap
msgid "ui-via-backend.png"
msgstr "ui-via-backend.png"

#. type: Title =
#: introduction.adoc:1
#, no-wrap
msgid "Introduction"
msgstr "Einleitung"

#. type: Target for macro image
#: introduction.adoc:4 introduction.de.adoc:4
#, no-wrap
msgid "OpenEMS-Logo.png"
msgstr "OpenEMS-Logo.png"

#. type: Title ==
#: introduction.adoc:6
#, no-wrap
msgid "Open Energy Management System"
msgstr "Offenes Energiemanagementsystem"

#. type: Title ==
#: introduction.adoc:12
#, no-wrap
msgid "OpenEMS IoT stack"
msgstr "OpenEMS IoT Stack"

#. type: Plain text
#: introduction.adoc:15
msgid "The OpenEMS 'Internet of Things' stack contains three main components:"
msgstr "Der OpenEMS \"Internet of Things\"-Stack enthält drei Hauptkomponenten:"

#. type: Plain text
#: introduction.adoc:17
#, no-wrap
msgid "**OpenEMS Edge** runs on-site and actually controls the devices\n"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte\n"

#. type: Plain text
#: introduction.adoc:18
#, no-wrap
msgid "**OpenEMS UI** is the generic user interface\n"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche\n"

#. type: Plain text
#: introduction.adoc:19
#, no-wrap
msgid "**OpenEMS Backend** runs on a (cloud) server, connects the decentralized Edge systems and provides aggregation, monitoring and control via internet\n"
msgstr "**OpenEMS Backend** läuft auf einem (Cloud) Server, verbindet die dezentralen Edge-Systeme und sorgt für Aggregation, Überwachung und Steuerung über das Internet\n"

#. type: Title ==
#: introduction.adoc:20
#, no-wrap
msgid "Features"
msgstr "Funktionen"

#. type: Plain text
#: introduction.adoc:23
msgid "The OpenEMS software architecture was designed to leverage some features that are required by a modern and flexible Energy Management System:"
msgstr "Die OpenEMS-Softwarearchitektur wurde entwickelt, um Funktionen bereitzustellen, die ein modernes und flexibles Energiemanagementsystem benötigt:"

#. type: Plain text
#: introduction.adoc:25
msgid "Fast, PLC-like control of battery inverters and other devices"
msgstr "Schnelle, SPS-ähnliche Steuerung von Batteriewechselrichtern und anderen Geräten"

#. type: Plain text
#: introduction.adoc:26
msgid "Easily extendable due to the use of modern programming languages and modular architecture"
msgstr "Leicht erweiterbar durch den Einsatz moderner Programmiersprachen und modularer Architektur"

#. type: Plain text
#: introduction.adoc:27
msgid "Wide range of supported devices - (battery) inverters, meters, etc. - and protocols"
msgstr "Große Auswahl an unterstützten Geräten - (Batterie-)Wechselrichter, Zähler, etc. - und Protokolle"

#. type: Plain text
#: introduction.adoc:28
msgid "Modern web-based real-time user interface"
msgstr "Moderne webbasierte Echtzeit-Benutzeroberfläche"

#. type: Title ==
#: introduction.adoc:29 introduction.de.adoc:36
#, no-wrap
msgid "OpenEMS UI Screenshots"
msgstr "OpenEMS UI Screenshots"

#. type: Target for macro image
#: introduction.adoc:30 introduction.de.adoc:37
#, no-wrap
msgid "ui-screenshots.png"
msgstr "ui-screenshots.png"

#. type: Target for macro image
#: introduction.adoc:31 introduction.de.adoc:38
#, no-wrap
msgid "ui-screenshots2.png"
msgstr "ui-screenshots2.png"

#. type: Title =
#: introduction.de.adoc:1
#, no-wrap
msgid "Einleitung"
msgstr "Einleitung"

#. type: Title ==
#: introduction.de.adoc:6
#, no-wrap
msgid "Offenes Energiemanagementsystem"
msgstr "Offenes Energiemanagementsystem"

#. type: Plain text
#: introduction.de.adoc:12
msgid "OpenEMS ist eine modulare Plattform für Energiemanagement-Anwendungen.  Es wurde um die Anforderungen der Steuerung, Überwachung und Integration von Energiespeichern zusammen mit erneuerbaren Energiequellen und ergänzenden Geräten und Dienstleistungen entwickelt."
msgstr "OpenEMS ist eine modulare Plattform für Energiemanagement-Anwendungen.  Es wurde um die Anforderungen der Steuerung, Überwachung und Integration von Energiespeichern mit erneuerbaren Energiequellen und ergänzenden Geräten und Dienstleistungen entwickelt."

#. type: Title ==
#: introduction.de.adoc:14
#, no-wrap
msgid "OpenEMS IoT Stack"
msgstr "OpenEMS IoT Stack"

#. type: Plain text
#: introduction.de.adoc:17
msgid "Der OpenEMS \"Internet of Things\"-Stack enthält drei Hauptkomponenten:"
msgstr "Der OpenEMS \"Internet of Things\"-Stack enthält drei Hauptkomponenten:"

#. type: Plain text
#: introduction.de.adoc:19
#, no-wrap
msgid "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte\n"
msgstr "* * OpenEMS Edge * * läuft vor Ort und steuert die lokalen Geräte\n"

#. type: Plain text
#: introduction.de.adoc:20
#, no-wrap
msgid "**OpenEMS UI** ist die generische Benutzeroberfläche\n"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche\n"

#. type: Plain text
#: introduction.de.adoc:21
#, no-wrap
msgid "**OpenEMS Backend** läuft auf einem (Cloud) Server, verbindet die dezentralen Edge-Systeme und sorgt für Aggregation, Überwachung und Steuerung über das Internet\n"
msgstr "**OpenEMS Backend** läuft auf einem (Cloud) Server, verbindet die dezentralen Edge-Systeme und sorgt für Aggregation, Überwachung und Steuerung über das Internet\n"

#. type: Title ==
#: introduction.de.adoc:22
#, no-wrap
msgid "Funktionen"
msgstr "Funktionen"

#. type: Plain text
#: introduction.de.adoc:27
msgid "Die OpenEMS-Softwarearchitektur wurde entwickelt, um Funktionen bereitzustellen, die ein modernes und flexibles Energiemanagementsystem benötigt:"
msgstr "Die OpenEMS-Softwarearchitektur wurde entwickelt, um Funktionen bereitzustellen, die ein modernes und flexibles Energiemanagementsystem benötigt:"

#. type: Plain text
#: introduction.de.adoc:30
msgid "Schnelle, SPS-ähnliche Steuerung von Batteriewechselrichtern und anderen Geräten"
msgstr "Schnelle, SPS-ähnliche Steuerung von Batteriewechselrichtern und anderen Geräten"

#. type: Plain text
#: introduction.de.adoc:32
msgid "Leicht erweiterbar durch den Einsatz moderner Programmiersprachen und modularer Architektur"
msgstr "Leicht erweiterbar durch den Einsatz moderner Programmiersprachen und modularer Architektur"

#. type: Plain text
#: introduction.de.adoc:34
msgid "Große Auswahl an unterstützten Geräten - (Batterie-)Wechselrichter, Zähler, etc. - und Protokolle"
msgstr "Große Auswahl an unterstützten Geräten-(Batterie-) Wechselrichter, Zähler, etc.-und Protokolle"

#. type: Plain text
#: introduction.de.adoc:35
msgid "Moderne webbasierte Echtzeit-Benutzeroberfläche"
msgstr "Moderne webbasierte Echtzeit-Benutzeroberfläche"

#. type: Title =
#: openems_as.adoc:1
#, no-wrap
msgid "OpenEMS Association"
msgstr "OpenEMS Association"

#. type: Plain text
#: openems_as.adoc:13
msgid "Zur Förderung der Weiterentwicklung von OpenEMS wurde am 15.11.2018 die \"OpenEMS Association\" gegründet."
msgstr "Zur Förderung der Weiterentwicklung von OpenEMS wurde am 15.11.2018 die \"OpenEMS Association\" gegründet."

#. type: Block title
#: openems_as.adoc:14
#, no-wrap
msgid "Gründungsmitglieder"
msgstr "Gründungsmitglieder"

#. type: Target for macro image
#: openems_as.adoc:15
#, no-wrap
msgid "members.JPG"
msgstr "members.JPG"

#. type: Plain text
#: openems_as.adoc:18
msgid "Die zugehörige Pressemitteilung finden Sie link:https://github.com/OpenEMS/openems/raw/develop/doc/Pressemitteilung_OpenEMS_Association_Gruendung_1v0.pdf[hier zum Download]."
msgstr "Die zugehörige Pressemitteilung finden Sie link:https://github.com/OpenEMS/openems/raw/develop/doc/Pressemitteilung_OpenEMS_Association_Gruendung_1v0.pdf[hier zum Download]."

#. type: Title ==
#: openems_as.adoc:19
#, no-wrap
msgid "PV Magazine Top Business Model Award"
msgstr "PV Magazin Top Business Model Award"

#. type: Block title
#: openems_as.adoc:21
#, no-wrap
msgid "PV Magazine Award"
msgstr "PV Magazine Award"

#. type: Target for macro image
#: openems_as.adoc:22
#, no-wrap
msgid "pvmagazine.png"
msgstr "pvmagazine.png"

#. type: Plain text
#: openems_as.adoc:25
msgid ""
"Auch das PV Magazine erkennt das Potenzial eines Betriebssystems für die Energiewende und würdigt die OpenEMS Association bereits zum Start mit dem Top Business Model Award. Im Auftrag von Chefredakteur Dr. Michael Fuhs durfte Franz-Josef Feilmeier, CEO der FENECON GmbH, die Urkunde an seinen Bruder Stefan Feilmeier, frisch gewählter Vorstandsvorsitzender der OpenEMS Association, überreichen. Der Preisverleihung "
"vorausgegangen war eine lesenswerte Diskussion über die Notwendigkeit und Möglichkeiten von Energiemanagementsystemen zwischen Franz-Josef Feilmeier und Hans Urban, die im Zuge der Verleihung link:https://www.pv-magazine.de/2018/11/12/pv-magazine-top-business-model-ein-betriebssystem-fuer-die-energiewende/?utm_source=CleverReach&utm_medium=email&utm_campaign=27-11-2018+FENECON+November"
"+Newsletter&utm_content=Mailing_7205600[ebenfalls veröffentlicht wurde]."
msgstr ""
"Auch das PV Magazine erkennt das eines Potenzial Betriebssystems für die Energiewende und würdigt die OpenEMS Association bereits zum Start mit dem Top Business Model Award. Im Auftrag von Chefredakteur Dr. Michael Fuhs durfte Franz-Josef Feilmeier, Geschäftsführer der FENECON GmbH, die Urkunde an seinen Bruder Stefan Feilmeier, frisch gewählter Vorstandsvorsitzender der OpenEMS Association, überreichen. Der Preisverleihung "
"vorausgegangen war eine lesenswerte Diskussion über die Notwendigkeit und Möglichkeiten von Energiemanagementsystemen zwischen Franz-Josef Feilmeier und Hans Urban, die im Zuge der Verleihung link:https://www.pv-magazine.de/2018/11/12/pv-magazine-top-business-model-ein-betriebssystem-fuer-die-energiewende/?utm_source=CleverReach&utm_medium=email&utm_campaign=27-11-2018+FENECON+November"
"+Newsletter&utm_content=Mailing_7205600[ebenfalls veröffentlicht wurde]."

#. type: Title ==
#: openems_as.adoc:27
#, no-wrap
msgid "OpenEMS Association Newsletter"
msgstr "Newsletter der OpenEMS Association"

#. type: Plain text
#: openems_as.adoc:30
msgid "Gerne halten wir Sie über aktuelle News zur OpenEMS Association auf dem Laufenden. Bitte melden Sie sich zu unserem Newsletter an:"
msgstr "Gerne halten wir Sie über aktuelle News zur OpenEMS Association auf dem Laufenden. Bitte melden Sie sich zu unserem Newsletter an:"

#. type: Plain text
#: openems_as.adoc:32
msgid "link:https://seu2.cleverreach.com/f/207318-204802/[Anmeldung OpenEMS Association Newsletter]"
msgstr "link:https://seu2.cleverreach.com/f/207318-204802/[Anmeldung OpenEMS Association Newsletter]"

#. type: Title ==
#: openems_as.adoc:33
#, no-wrap
msgid "Ziele des Vereins"
msgstr "Ziele des Vereins"

#. type: Plain text
#: openems_as.adoc:36
msgid ""
"Zwecke des Vereins sind die Entwicklung und kontinuierliche Pflege einer open-source Energiemanagementsystem-Plattform (OpenEMS) sowie die Beförderung deren verbreiteter Nutzung. Der Verein unterstützt die Entwicklung von Energiemanagement-Applikationen, die auf der OpenEMS-Plattform aufbauen. Durch die OpenEMS-Plattform soll eine effizientere und effektivere Anbindung und Steuerung technischer Anlagen bei gleichzeitiger "
"Verbreiterung deren Anwendungsmöglichkeiten erreicht werden. Damit soll ein Beitrag zu einer sicheren, wirtschaftlich und sozial verträglichen Energieversorgung der Zukunft auf Basis ernoeuerbarer Energien unter Schonung von Ressourcen und Umwelt geleistet werden. Dieser Zweck wird insbesondere verwirklicht durch"
msgstr ""
"Zwecke des Vereins sind die Entwicklung und kontinuierliche Pflege einer Open-Source Energiemanagementsystem-Plattform (OpenEMS) sowie die Beförderung deren verbreiteter Nutzung. Der Verein unterstützt die Entwicklung von Energiemanagement-Applikationen, die auf der OpenEMS-Plattform aufbauen. Durch die OpenEMS-Plattform soll eine effizientere und effektivere Anbindung und Steuerung technischer Anlagen bei gleichzeitiger "
"Verbreiterung deren Anwendungsmöglichkeiten erreicht werden. Damit soll ein Beitrag zu einer sicheren, wirtschaftlich und sozial verträglichen Energieversorgung der Zukunft auf Basis ernoeuerbarer Energien unter Schonung von Ressourcen und Umwelt geleistet werden. Dieser Zweck wird insbesondere verwirklicht durch"

#. type: Plain text
#: openems_as.adoc:38
msgid ""
"Förderung der Erforschung und Entwicklung von Software-Architekturen, Schnittstellen, Protokollen, Funktionen und Prozessen zum Aufbau, der kontinuierlichen Weiterentwicklung und dem Einsatz einer open-source Energiemanagementsystem-Plattform (OpenEMS) (z. B. durch Unterstützung und Koordination von Entwicklungsaktivitäten der Vereinsmitglieder sowie durch Initiierung und Durchführung von Forschungsprojekten unter Beteiligung "
"akademischer Einrichtungen oder gemeinnütziger Institute)"
msgstr ""
"Förderung der Erforschung und Entwicklung von Software-Architekturen, Schnittstellen, Protokollen, Funktionen und Prozessen zum Aufbau, der kontinuierlichen Weiterentwicklung und dem Einsatz einer Open-Source Energiemanagementsystem-Plattform ( OpenEMS) (z. B. durch Unterstützung und Koordination von Entwicklungsaktivitäten der Vereinsmitglieder sowie durch Initiierung und Durchführung von Forschungsprojekten unter "
"Beteiligung akademischer Einrichtungen oder gemeinnütziger Institute)"

#. type: Plain text
#: openems_as.adoc:40
msgid "Allgemeine Unterstützung der Erforschung, Entwicklung und Nutzung von Energiemanagement-Applikationen, die auf dem Einsatz der OpenEMS Plattform aufbauen (z. B. durch Vergabe von Nutzungsrechten an den Entwicklungsergebnissen der OpenEMS-Plattform, Unterstützung bei deren Verwendung sowie durch Information relevanter Nutzergruppen)"
msgstr "Allgemeine Unterstützung der Erforschung, Entwicklung und Nutzung von Energiemanagement-Applikationen, die auf dem Einsatz der OpenEMS Plattform aufbauen (z. B. durch Vergabe von Nutzungsrechten an den Entwicklungsergebnissen der OpenEMS-Plattform, Unterstützung bei deren Verwendung sowie durch Information relevanter Nutzergruppen)"

#. type: Plain text
#: openems_as.adoc:42
msgid "Förderung und Umsetzung von Initiativen zur Qualitätssicherung von OpenEMS -basierten Anwendungen zum Zwecke der funktionalen Sicherheit, relevanter Leistungsmerkmale, Einhaltung regulatorischer Bestimmungen sowie geltender Normen und der Interkompatibilität untereinander (z. B. durch Bildung von Facharbeitsgruppen, Zusammenarbeit mit Verbänden und Vereinen oder Beauftragung akademischer Einrichtungen)"
msgstr "Förderung und Umsetzung von Initiativen zur Qualitätssicherung von OpenEMS-basierten Anwendungen zum Zwecke der funktionalen Sicherheit, relevanter Leistungsmerkmale, Einhaltung regulatorischer Bestimmungen sowie geltender Normen und der Interkompatibilität untereinander (z. B. durch Bildung von Facharbeitsgruppen, Zusammenarbeit mit Verbänden und Vereinen oder Beauftragung akademischer Einrichtungen)"

#. type: Plain text
#: openems_as.adoc:44
msgid "Förderung der Verbreitung und des Einsatzes von OpenEMS und darauf basierter Anwendungen (z. B. durch Informations- und Demonstrationsveranstaltungen)"
msgstr "Förderung der Verbreitung und des Einsatzes von OpenEMS und darauf basierter Anwendungen (z. B. durch Informations-und Demonstrationsveranstaltungen)"

#. type: Plain text
#: openems_as.adoc:47
msgid "Förderung und Unterstützung von Qualifizierungsinitiativen zur Gewährleistung des Fachkräftenachwuchses (z. B. durch Veranstaltung von Seminaren oder Workshops)"
msgstr "Förderung und Unterstützung von Qualifizierungsinitiativen zur Gewährleistung des Fachkräftenachwuchses (z. B. durch Veranstaltung von Seminaren oder Workshops)"

#. type: Plain text
#: openems_as.adoc:49
msgid "Förderung der Diskussion und Vernetzung von Unternehmen und akademischen Einrichtungen zum Zwecke des Know-how Zugewinns und der vorausschauenden Entwicklungsplanung (z. B. durch Facharbeitsgruppen und Veranstaltungen)"
msgstr "Förderung der Diskussion und Vernetzung von Unternehmen und Aktuellen Einrichtungen zum Zwecke des Know-how-Zugewinns und der vorausschauenden Entwicklungsplanung (z. B. durch Facharbeitsgruppen und Veranstaltungen)"

#. type: Plain text
#: openems_as.adoc:51
msgid "Information der Öffentlichkeit und Nutzergruppen zu Grundlagen, Zielen und Inhalten von OpenEMS (insb. durch eine Internet-basierte Informationsplattform, Veranstaltungen, Demonstrationszentren)"
msgstr "Information der Öffentlichkeit und Nutzergruppen zu Grundlagen, Zielen und Inhalten von OpenEMS (insb. durch eine Internet-basierte Informationsplattform, Veranstaltungen, Demonstrationszentren)"

#. type: Plain text
#: openems_as.adoc:53
msgid "Unterstützung von Normungs- und Standardisierungsinitiativen (z. B. durch Bildung von Facharbeitskreisen oder fachlichen Beiträgen zu entsprechenden Initiativen)"
msgstr "Unterstützung von Normungs-und Standardisierungsinitiativen (z. B. durch Bildung von Facharbeitskreisen oder fachlichen Beiträgen zu entsprechenden Initiativen)"

#. type: Plain text
#: openems_as.adoc:55
msgid "Unterstützung der Mitglieder bei der Beantragung öffentlicher Fördermittel auf nationaler und internationaler Ebene und der Koordination geförderter Projekte (z. B. durch Initiierung und Information über geeignete Förderinitiativen)"
msgstr "Unterstützung der Mitglieder bei der Beanöffentlicher Fördermittel auf nationaler und internationaler Ebene und der Koordination geförderter Projekte (z. B. durch Initiierung und Information über geeignete Förderinitiativen)"

#. type: Plain text
#: openems_as.adoc:58
msgid "Vernetzung mit Initiativen im nationalen und internationalen Umfeld (z. B. durch Teilnahme an Konferenzen, Engagement in Verbänden oder über geeignete Kooperationsabkommen)"
msgstr "Vernetzung mit Initiativen im nationalen und internationalen Umfeld (z. B. durch Teilnahme an Konferenzen, Engagement in Verbänden oder über geeignete Kooperationsabkommen)"

#. type: Title ==
#: openems_as.adoc:59
#, no-wrap
msgid "Liste der Mitglieder "
msgstr "Liste der Mitglieder"

#. type: Plain text
#: openems_as.adoc:92
msgid ""
"•\tBayernwerk AG •\tConsolinno Energy GmbH •\tDiscovergy GmbH •\tegrid application + consulting •\tFENECON GmbH •\tFE-Partner AG, FL •\tFetron Hard- und Software GmbH •\tFieber Kai •\tFraunhofer-ISE •\tFS engineering & consulting •\tHeyer Georg •\tIBC-Solar AG •\tKACO New Energy GmbH •\tKNX Association •\tLEE Cooperative •\tMaschinenfabrik Reinhausen GmbH •\tMussack •\tMVV Energie AG •\tNATURAL-Energy – Energie-Umwelt-"
"Konzepte UG •\tNothaft Neue Heizsysteme GmbH •\tOxygen Technologies GmbH •\tREFU Eletronik •\tRegionalwerke GmbH & Co. KG •\tSEEL GmbH •\tStadtwerk Haßfurt GmbH •\tStadtwerke Speyer GmbH •\tStoREgio Energiespeicher Systeme e.V.  •\tStorion Energy GmbH •\tStromdao UG •\tVARTA Storage GmbH •\tWinter, Peter"
msgstr ""
"• Bayernwerk AG • Consolinno Energy GmbH • Discovergy GmbH • egrid application + consulting • FENECON GmbH • FE-Partner AG, FL • Fetron Hard-und Software GmbH • Fieber Kai • Fraunhofer-ISE • FS Engineering & Consulting • Heyer Georg • IBC-Solar AG • KACO New Energy GmbH • K NX-Verein • LEE Cooperative • Maschinenfabrik Reinhausen GmbH • Mussack • MVV Energie AG • NATURAL-Energy – Energie-Umwelt-Konzepte UG • Nothaft Neue "
"Heizsysteme GmbH • Oxygen Technologies GmbH • REFU Eletronik • Regionalwerke GmbH & Co. KG •\tStadtwerk Haßfurt GmbH • Stadtwerke Speyer GmbH • StoREgio Energiespeicher Systeme e.V.  • Storion Energy GmbH • Stromdao UG • VARTA Storage GmbH • Winter, Peter"

#. type: Title ==
#: openems_as.adoc:93
#, no-wrap
msgid "Vorstand"
msgstr "Vorstand"

#. type: Title ===
#: openems_as.adoc:95
#, no-wrap
msgid "E-Mail: <EMAIL>"
msgstr "E-Mail: <EMAIL>"

#. type: Plain text
#: openems_as.adoc:98
msgid "Der Vorstand besteht aus..."
msgstr "Der Vorstand besteht aus ..."

#. type: Plain text
#: openems_as.adoc:100
msgid "Vorsitzender:"
msgstr "Vorsitzender:"

#. type: Labeled list
#: openems_as.adoc:100
#, no-wrap
msgid "Stefan Feilmeier"
msgstr "Stefan Feilmeier"

#. type: Plain text
#: openems_as.adoc:103
msgid "<NAME_EMAIL>"
msgstr "<NAME_EMAIL>"

#. type: Plain text
#: openems_as.adoc:104
msgid "Stellvertretender Vorsitzender:"
msgstr "Stellvertretender Vorsitzender:"

#. type: Labeled list
#: openems_as.adoc:104
#, no-wrap
msgid "Alexander Hirnet"
msgstr "Alexander Hirnet"

#. type: Plain text
#: openems_as.adoc:107
msgid "VARTA <NAME_EMAIL>"
msgstr "VARTA <NAME_EMAIL>"

#. type: Plain text
#: openems_as.adoc:109
msgid "Schatzmeister"
msgstr "Schatzmeister"

#. type: Labeled list
#: openems_as.adoc:109
#, no-wrap
msgid "Ludwig Asen"
msgstr "Ludwig Asen"

#. type: Plain text
#: openems_as.adoc:112
msgid "  <NAME_EMAIL>"
msgstr "<NAME_EMAIL>"

#. type: Plain text
#: openems_as.adoc:113
msgid "Mitglieder:"
msgstr "Mitglieder:"

#. type: Labeled list
#: openems_as.adoc:113
#, no-wrap
msgid "Alexander Netzer"
msgstr "Alexander Netzer"

#. type: Plain text
#: openems_as.adoc:116
msgid "<NAME_EMAIL>"
msgstr "<NAME_EMAIL>"

#. type: Labeled list
#: openems_as.adoc:116
#, no-wrap
msgid "Christof Wiedmann"
msgstr "Christof Wiedmann"

#. type: Plain text
#: openems_as.adoc:119
msgid "<NAME_EMAIL>"
msgstr "<NAME_EMAIL>"

#. type: Labeled list
#: openems_as.adoc:119
#, no-wrap
msgid "Florian Priller"
msgstr "Florian Priller"

#. type: Plain text
#: openems_as.adoc:122
msgid "<NAME_EMAIL>"
msgstr "<NAME_EMAIL>"

#. type: Labeled list
#: openems_as.adoc:122
#, no-wrap
msgid "Klaus Nagl"
msgstr "Klaus Nagl"

#. type: Plain text
#: openems_as.adoc:125
msgid "Consolinno <NAME_EMAIL>"
msgstr "Consolinno <NAME_EMAIL>"

#. type: Labeled list
#: openems_as.adoc:125
#, no-wrap
msgid "Florian Kind"
msgstr "Florian Kind"

#. type: Plain text
#: openems_as.adoc:128
msgid "FE <NAME_EMAIL>"
msgstr "FE <NAME_EMAIL>"

#. type: Labeled list
#: openems_as.adoc:128
#, no-wrap
msgid "Thorsten Zörner"
msgstr "Thorsten Zörner"

#. type: Plain text
#: openems_as.adoc:131
msgid "<NAME_EMAIL>"
msgstr "<NAME_EMAIL>"

#. type: Title ==
#: openems_as.adoc:132
#, no-wrap
msgid "Beitragsordnung"
msgstr "Beitragsordnung"

#. type: Plain text
#: openems_as.adoc:135
msgid "link:https://github.com/OpenEMS/openems/raw/develop/doc/OpenEMS%20Association%20e.V.%20Gr%C3%BCndungs-Beitragsordnung.pdf[OpenEMS Association e.V. Gründungs-Beitragsordnung]"
msgstr "link:https://github.com/OpenEMS/openems/raw/develop/doc/OpenEMS%20Association%20e.V.%20Gr%C3%BCndungs-Beitragsordnung.pdf[OpenEMS Association e.V. Gründungs-Beitragsordnung]"

#. type: Title ==
#: openems_as.adoc:136
#, no-wrap
msgid "Antrag auf Mitgliedschaft"
msgstr "Antrag auf Mitgliedschaft"

#. type: Plain text
#: openems_as.adoc:138
msgid "link:https://github.com/OpenEMS/openems/raw/develop/doc/OpenEMS%20Association%20e.V.%20Antrag%20auf%20Mitgliedschaft.pdf[OpenEMS Association e.V. Antrag auf Mitgliedschaft]"
msgstr "link:https://github.com/OpenEMS/openems/raw/develop/doc/OpenEMS%20Association%20e.V.%20Antrag%20auf%20Mitgliedschaft.pdf[OpenEMS Association e.V. Antrag auf Mitgliedschaft]"

#. type: Title =
#: randd.adoc:1
#, no-wrap
msgid "Research and Development"
msgstr "Forschung und Entwicklung"

#. type: Title ==
#: randd.adoc:7
#, no-wrap
msgid "EASY-RES"
msgstr "EASY-RES"

#. type: Title ===
#: randd.adoc:9
#, no-wrap
msgid "Enable Ancillary Services by Renewable Energy Sources"
msgstr "Verkaufende Dienstleistungen durch erneuerbare Energiequellen"

#. type: Target for macro image
#: randd.adoc:11
#, no-wrap
msgid "ezrs.png"
msgstr "ezrs.png"

#. type: Plain text
#: randd.adoc:14
msgid "Im Rahmen des EASY-RES-Projekts soll erforscht werden, wie die Versorgungssicherheit mit elektrischer Energie erhalten werden kann, während sich die Energieerzeugung von konventionellen Kraftwerken hin zu 100 % erneuerbaren Energien wendet. Dieser Wandel wird auch große Auswirkungen auf die Umwelt haben und zur Lösung der globalen Herausforderungen im Bereich Klima und Energie beitragen."
msgstr "Im Rahmen des EASY-RES-Projekts soll erforscht werden, wie die Versorgungssicherheit mit elektrischer Energie erhalten werden kann, während sich die Energieerzeugung von konventionellen Kraftwerken hin zu 100% erneuerbaren Energien wendet. Dieser Wandel wird auch große Auswirkungen auf die Umwelt haben und zur Lösung der globalen Herausforderungen im Bereich Klima und Energie beitragen."

#. type: Plain text
#: randd.adoc:17
msgid ""
"Am EASY-RES-Projekt beteiligen sich ausgewählte Partner aus sechs EU-Ländern. Zum Konsortium zählen neben FENECON fünf Universitäten (https://www.uni-passau.de/[Passau icon:external-link[]] , https://https://www.auth.gr/en[Thessaloniki icon:external-link[]], http://www.us.es/eng[Sevilla icon:external-link[]], https://www.tudelft.nl/[Delft icon:external-link[]] und https://www.lancaster.ac.uk/[Lancaster icon:external-link[]]), "
"drei Energieversorger (https://www.swlandau.de/[Stadtwerke Landau a. d. Isar icon:external-link[]], http://www.stadtwerkhassfurt.de/[Stadtwerk Haßfurt icon:external-link[]] und https://www.elektro-gorenjska.si/[Elektro Gorenjska icon:external-link[]] aus der Slowakei), der http://www.admie.gr/nc/en/home/<USER>//zentrum-digitalisierung.bayern/[Zentrum "
"Digitalisierung.Bayern icon:external-link[]]."
msgstr ""
"Am EASY-RES-Projekt beteiligen sich ausgewählte Partner aus sechs EU-Ländern. Zum Konsortium zählen neben FENECON fünf Universitäten (https://www.uni-passau.de/[Passau icon:external-link[]], https:/https://www.auth.gr/en [Thessaloniki icon:external-link[]], http://www.us.es/eng [Sevilla icon:external-link[]], https://www.tudelft.nl/[Delft icon:external-link[]] und https://www.lancaster.ac.uk/[Lancaster icon:external-link[]), drei "
"Energieversorger (https://www.swlandau.de/[Stadtwerke Landau a. d. Isar icon:external-link[]], http://www.stadtwerkhassfurt.de/[Stadtwerk Haßfurt icon:external-link[]] und https://www.elektro-gorenjska.si/[Elektro Gorenjska icon:external-link[] aus der Slowakei), der http://www.admie.gr/nc/en/home/<USER>/zentrum-digitalisierung.bayern/[Zentrum "
"Digitalisierung.Bayern icon:external-link[]]."

#. type: Plain text
#: randd.adoc:19
msgid ""
"FENECON wird technisches Fachwissen über das Batterieverhalten unter stationären und transienten Bedingungen sowie zu den Regelungsalgorithmen beitragen. Darüber hinaus werden wir uns an der Entwicklung von neuen Systemdienstleistungen durch Speichersysteme beteiligen und die erforderlichen Speichergeräte in den Labortests bereitstellen. Als Basis für das Energiemanagement dient das von FENECON initiierte Open Source "
"Energiemanagement https://openems.io[OpenEMS icon:external-link[]], das im Rahmen des Projektes weiterentwickelt wird."
msgstr ""
"FENECON wird technisches Fachwissen über das Batterieverhalten unter stationären und transienten Bedingungen sowie zu den Regelungsalgorithmen beitragen. Darüber hinaus werden wir uns an der Entwicklung von neuen Systemdienstleistungen durch Speichersysteme beteiligen und die erforderlichen Speichergeräte in den Labortests bereitstellen. Als Basis für das Energiemanagement dient das von FENECON initiierte Open Source "
"Energiemanagement https:/openems.io [OpenEMS icon:external-link[], das im Rahmen des Projektes weiterentwickelt wird."

#. type: Plain text
#: randd.adoc:21
msgid "Mehr Informationen: http://www.easyres-project.eu/"
msgstr "Mehr Informationen: http://www.easyres-project.eu/"

#. type: Title ==
#: randd.adoc:22
#, no-wrap
msgid "EMSIG"
msgstr "EMSIG"

#. type: Title ===
#: randd.adoc:24
#, no-wrap
msgid "Energiemanagementsystem für integrierte Geschäftsmodelle"
msgstr "Energiemanagementsystem für integrierte Geschäftsmodelle"

#. type: Target for macro image
#: randd.adoc:26
#, no-wrap
msgid "emsig.png"
msgstr "emsig.png"

#. type: Plain text
#: randd.adoc:29
msgid "Verteilte Stromspeichersysteme können einen großen Beitrag zur Energiewende leisten. Die derzeit verwendeten Betriebsweisen bieten aber meist nur naive Strategien, die ausschließlich die momentane Situation hinter dem Stromzähler berücksichtigen."
msgstr "Verteilte Stromspeichersysteme können einen großen Beitrag zur Energiewende leisten. Die derzeit verwendeten Betriebsweisen bieten aber meist nur naive Strategien, die ausschließlich die momentane Situation hinter dem Stromzähler berücksichtigen."

#. type: Plain text
#: randd.adoc:31
msgid "Ziel des Projektes ist es, durch integrierte Geschäftsmodelle das große Potential von Speichern sowohl für den Endnutzer als auch den Verteilnetzbetreiber (VNB) zu erschließen. Dazu werden innovative Be- und Entladealgorithmen entwickelt, die die Eigennnutzung von Solarenergie und die Steuerung variabler Lasten optimieren."
msgstr "Ziel des Projektes ist es, durch integrierte Geschäftsmodelle das große Potential von Speichern sowohl für den Endnutzer als auch den Verteilnetzbetreiber (VNB) zu erschließen. Dazu werden innovative Be-und Entladeal-men entwickelt, die die Eigennnutzung von Solarenergie und die Steuerung variabler Lasten optimieren."

#. type: Plain text
#: randd.adoc:33
msgid "Durch \"Pooling\" der verteilten Speicherflexibilität wird außerdem die Teilnahme am Energiemarkt ermöglicht. Zudem wird eine Open-Data-Plattform für anonymisierte Energiedaten erstellt, um diese für Forschungszwecke öffentlich zugänglich zu machen. Basis des Projektes ist das Open-Source Energiemanagementsystem https://openems.io[OpenEMS icon:external-link[]], das im Rahmen des Projektes weiterentwickelt wird."
msgstr "Durch \"Pooling\" der verteilten Speicherflexibilität wird außerdem die Teilnahme am Energiemarkt ermöglicht. Zudem wird eine Open-Data-Plattform für anonymisierte Energiedaten erstellt, um diese für Forschungszwecke öffentlich zugänglich zu machen. Basis des Projektes ist das Open-Source Energiemanagementsystem https:/openems.io [OpenEMS icon:external-link[]], das im Rahmen des Projektes weiterentwickelt wird."

#. type: Title ==
#: randd.adoc:35
#, no-wrap
msgid "BloGPV"
msgstr "BloGPV"

#. type: Title ===
#: randd.adoc:37
#, no-wrap
msgid "Blockchainbasierter, verteiter Großspeicher für PV-Anlagenbetreiber"
msgstr "Blockchainbasierter, verteiter Großspeicher für PV-Anlagenbetreiber"

#. type: Target for macro image
#: randd.adoc:39
#, no-wrap
msgid "blogpv.png"
msgstr "blogpv.png"

#. type: Plain text
#: randd.adoc:42
msgid "BloGPV hat sich zum Ziel gesetzt einen wirtschaftlichen Betrieb von Photovoltaikanlagen in einer Post-EEG Situation zu ermöglichen. Als Teil der Smart Service Welt II sollen viele dezentrale Batteriespeicher durch die Blockchain-Technologie zu einem virtuellen Großspeicher vereint werden."
msgstr "BloGPV hat sich zum Ziel gesetzt einen wirtschaftlichen Betrieb von Photovoltaikanlagen in einer Post-EEG Situation zu ermöglichen. Als Teil der Smart Service Welt II sollen viele dezentrale Batteriespeicher durch die Blockchain-Technologie zu einem virtuellen Großspeicher vereint werden."

#. type: Plain text
#: randd.adoc:44
msgid "Ein dezentrales Speichermanagement mit der Anbindung von Mehrwertdiensten sowie der Schaffung geeigneter Bilanzierungs- und Abrechnungsmodelle soll ein einen wirtschaftlichen Betrieb der PV-Anlagen ohne Einspeisevergütung zu ermöglichen und somit einen wesentlichen Beitrag zur Energiewende leisten."
msgstr "Ein dezentrales Speichermanagement mit der Anbindung von Mehrwertdiensten sowie der Schaffung geeigneter Bilanzierungs-und Abrechnungsmodelle soll ein einen wirtschaftlichen Betrieb der PV-Anlagen ohne Einspeisevergütung zu ermöglichen und somit einen wesentlichen Beitrag zur Energiewende leisten."

#. type: Plain text
#: randd.adoc:46
msgid "Fenecon arbeitet hierzu mit den Projektpartnern https://www.dfki.de/web/[Deutsches Forschungszentrum für künstliche Intelligenz icon:external-link[]], https://discovergy.com/[Discovergy icon:external-link[]], https://www.enercity.de/privatkunden/index.html[enercity icon:external-link[]] und der https://www.tu-berlin.de/menue/home/<USER>"
msgstr "Fenecon arbeitet hierzu mit den Projektpartnern https://www.dfki.de/web/[Deutsches Forschungszentrum für künstliche Intelligenz icon:external-link[]], https://tasy.com/[Discovergy icon:external-link[]], https://www.enercity.de/privatkunden/index.html [enercity icon:external-link[]] und der https://www.tu-berlin.de/menue/home/<USER>"

#. type: Plain text
#: randd.adoc:47
msgid "Mehr Informationen: https://blogpv.net/"
msgstr "Mehr Informationen: https://blogpv.net/"

#. type: Title =
#: simulation.adoc:1
#, no-wrap
msgid "Simulation"
msgstr "Simulation"

#. type: Plain text
#: simulation.adoc:8
msgid ""
"OpenEMS provides several virtual devices for testing and development. To set up the simulation-environment follow the setup guide in xref:gettingstarted.adoc[Getting Started]. The Apache Felix Web Console Configuration lists all OpenEMS component, but those which rely on external hardware can't be used for simulation. These are replaced by more generic components tagged with the \"Simulator\" prefix. Still, not all non-"
"simulator devices rely on real hardware. Other components used in production, like Loggers, Controllers and Schedulers can be combined with the virtual devices to build a \"living\" system."
msgstr ""
"OpenEMS bietet mehrere virtuelle Geräte zum Testen und Entwickeln. Um die Simulationsumgebung einzurichten, folgen Sie dem Setup-Leitfaden in xref: gettingstarted.adoc [Getting Started]. Die Apache Felix Web Console Configuration listet alle OpenEMS-Komponente auf, aber diejenigen, die auf externe Hardware angewiesen sind, können nicht für die Simulation verwendet werden. Diese werden durch mehr generische Komponenten "
"ersetzt, die mit dem Präfix \"Simulator\" versehen sind. Dennoch sind nicht alle Nicht-Simulator-Geräte auf echte Hardware angewiesen. Andere Komponenten, die in der Produktion zum Einsatz kommen, wie Logger, Controller und Scheduler, lassen sich mit den virtuellen Geräten kombinieren, um ein \"lebendiges\" System aufzubauen."

#. type: Title ==
#: simulation.adoc:9
#, no-wrap
msgid "Types of Simulated Components"
msgstr "Arten von simulierten Komponenten"

#. type: Plain text
#: simulation.adoc:12
#, no-wrap
msgid "**DataSource**: A data-source generates lists of data and provides them as a channel to other components. The data can be generated by different means, which could be an augmented random-generator or a reader for a specific data-format like the \"Simulator Datasource: CSV Reader\".\n"
msgstr "* * DataSource * *: Eine Datenquelle generiert Datenlisten und stellt sie als Kanal zu anderen Komponenten zur Verfügung. Die Daten können mit verschiedenen Mitteln generiert werden, die ein erweiterter Zufallsgenerator oder ein Reader für ein bestimmtes Datenformat wie den \"Simulator Datasource: CSV Reader\" sein könnten.\n"

#. type: Plain text
#: simulation.adoc:15
#, no-wrap
msgid ""
"**Devices**: Devices replace a hardware-component like a meter or an ess. There are two kinds of devices:\n"
"** **acting**: These devices act on their own. Their actions do not depend on other device's actions, but on data-sources.\n"
"** **reacting**: These devices analyse other device's channels/properties to deduce their own behavior.\n"
msgstr ""
"* * Geräte * *: Geräte ersetzen eine Hardware-Komponente wie ein Meter oder ein ess. Es gibt zwei Arten von Geräten:\n"
"* * * * Schauspiel * *: Diese Geräte handeln eigenständig. Ihre Handlungen hängen nicht von den Aktionen anderer Geräte ab, sondern von Datenquellen.\n"
"* * * * reagiert * *: Diese Geräte analysieren die Channels/-Eigenschaften anderer Geräte, um ihr eigenes Verhalten abzuleiten.\n"

#. type: Title ==
#: simulation.adoc:16
#, no-wrap
msgid "Simulator Components"
msgstr "Simulator-Komponenten"

#. type: Plain text
#: simulation.adoc:19
msgid "`Simulator DataSource: CSV Reader`: Reads .csv files and provides their content according to the `io.openems.edge.simulator.datasource.api.SimulatorDatasource` interface. Its package contains several sample csv-files for different devices and situations."
msgstr "' Simulator DataSource: CSV Reader ': Listet .csv-Dateien auf und stellt deren Inhalt nach der ' io.openems.edge.simulator.datasource.api.SimulatorDatasource '-Schnittstelle zur Verfügung. Sein Paket enthält mehrere Beispiele csv-Dateien für verschiedene Geräte und Situationen."

#. type: Plain text
#: simulation.adoc:20
msgid "`Simulator EssSymmetric Reacting`: Simulates the State of Charge of a ess device. Its behavior depends on configured controllers."
msgstr "' Simulator EssSymmetric Reacting ': Simuliert den Ladezustand eines Schachtes. Sein Verhalten hängt von konfigurierten Controllern ab."

#. type: Plain text
#: simulation.adoc:21
msgid "`Simulator GridMeter Acting`: Intended to represent grid consumption/production. Portrays a datasource' values. It requests those tagged with the \"ActivePower\" key. The values are portrayed in the `SimulatedActivePower`, `ActivePower` and - divided by three - in the `ActivePowerL1`, `ActivePowerL2` and `ActivePowerL3` channels."
msgstr "\"Simulator GridMeter Acting\": Ziel ist es, den Netzkonsum zu repräsentieren. Porträtiert die Werte eines Datencassource. Es fordert diejenigen, die mit der \"ActivePower\"-Taste markiert sind. Die Werte werden in den Kanälen ' SimulatedActivePower ', ' ActivePower ' und-geteilt durch drei-in den Kanälen ' ActivePowerL1 ', ' ActivePowerL2 ' und ' ActivePowerL3 ' dargestellt."

#. type: Plain text
#: simulation.adoc:22
msgid "`Simulator ProductionMeter Acting`: Intended to represent fixed production like photovoltaic arrays. To date the functionality equals the one of `Simulator GridMeter Acting`."
msgstr "\"Simulator ProductionMeter Acting\": Es soll eine feste Produktion wie Photovoltaik-Arrays darstellen. Bis heute entspricht die Funktionalität der von ' Simulator GridMeter Acting '."

#. type: Plain text
#: simulation.adoc:23
msgid "`Simulator NRCMeter Acting`: Intended to represent non-regulated-consumption. To date the functionality equals the one of `Simulator GridMeter Acting`."
msgstr "\"Simulator NRCMeter Acting\": Verwendet, um nicht regulierten Verbrauch darzustellen. Bis heute entspricht die Funktionalität der von ' Simulator GridMeter Acting '."

#. type: Plain text
#: simulation.adoc:24
msgid ""
"`Simulator GridMeter Reacting`: Intended to represent grid consumption/production. Takes all meters (but grid-meters) and esss, sums up their ActivePowers and divides this value by the number of grid-meters in the system. The negative of this result is then portrayed in the `SimulatedActivePower`, `ActivePower` and - divided by three - in the `ActivePowerL1`, `ActivePowerL2` and `ActivePowerL3` channels. This equals the "
"physical reaction of the real grid."
msgstr ""
"' Simulator GridMeter Reacting ': Ziel ist es, den Netzkonsum zu repräsentieren. Nimmt alle Meter (aber Gitterzähler) und esss, fasst ihre ActivePowers zusammen und teilt diesen Wert durch die Anzahl der Gittermeter im System. Das Negative dieses Ergebnisses wird dann in den Kanälen ' SimulatedActivePower ', ' ActivePower ' und-geteilt durch drei-in den ' ActivePowerL1 ', ' ActivePowerL2 ' und ' ActivePowerL3 '-Kanälen "
"dargestellt. Das entspricht der physischen Reaktion des realen Gitters."

#. type: Title ==
#: simulation.adoc:25
#, no-wrap
msgid "Possible Combinations"
msgstr "Mögliche Kombinationen"

#. type: Plain text
#: simulation.adoc:28
msgid "In reality the OpenEMS can not measure the non-regulated-consumers, as they are spread all over the building. However, it can measure the power at the mains connection, which can be used to calculate the non-regulated-consumption. This approach can be simulated using the `Simulator GridMeter Acting` combined with a `Simulator EssSymmetric Reacting`."
msgstr "In Wirklichkeit können die OpenEMS nicht die nicht-regulierten Verbraucher messen, da sie über das gesamte Gebäude verteilt sind. Er kann aber die Leistung am Netzanschluss messen, mit dem der nicht regulierte Verbrauch berechnet werden kann. Dieser Ansatz kann mit dem \"Simulator GridMeter Acting\" in Kombination mit einem \"Simulator EssSymmetric Reacting\" simuliert werden."

#. type: Plain text
#: simulation.adoc:29
msgid "For intelligent systems going beyond real-time-regulation, however, having a - even in production - virtual meter measuring the non-regulated-consumers can be a major advantage. In order to test algorithms depending on such an environment, the simulator provides the `Simulator ProductionMeter Acting`, the `Simulator NRCMeter Acting` and the `Simulator GridMeter Reacting`."
msgstr "Für intelligente Systeme, die über die Echtzeitregulierung hinausgehen, kann es jedoch ein großer Vorteil sein, wenn es um einen-auch in der Produktion-virtuellen Zähler geht, der die nicht-regulierten Verbraucher misst. Um Algorithmen je nach einer solchen Umgebung zu testen, bietet der Simulator den ' Simulator ProductionMeter Acting ', den ' Simulator NRCMeter Acting ' und den ' Simulator GridMeter Reacting '."

#. type: Title =
#: ui/build.adoc:1
#, no-wrap
msgid "Build OpenEMS UI"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche"

#. type: Plain text
#: ui/build.adoc:13
msgid "This chapter explains, how OpenEMS UI can be compiled so that it can be executed outside of an IDE."
msgstr "In diesem Kapitel wird erklärt, wie OpenEMS UI kompiliert werden kann, so dass es außerhalb einer IDE ausgeführt werden kann."

#. type: Plain text
#: ui/build.adoc:15
msgid "Open the terminal inside Visual Studio Code (Menu: btn:[Terminal] -> btn:[New Terminal])."
msgstr "Öffnen Sie das Terminal im Visual Studio Code (Menü: btn: [Terminal]-> btn: [Neues Terminal])."

#. type: Title ==
#: ui/build.adoc:16
#, no-wrap
msgid "Build using angular-cli from command line"
msgstr "Mit angular-cli aus der Kommandozeile bauen"

#. type: Plain text
#: ui/build.adoc:19
msgid "Execute the following commands inside the `ui` directory of your OpenEMS repository. The build artifacts will be stored in a subfolder of the `target` directory"
msgstr "Führen Sie die folgenden Befehle im ' ui '-Verzeichnis Ihres OpenEMS-Projektarchivs aus. Die Build-Artefakte werden in einem Unterordner des ' Zielverzeichnisses ' gespeichert"

#. type: Plain text
#: ui/build.adoc:21
msgid "To get more help on `angular-cli` use `ng help` or go check out the link:https://github.com/angular/angular-cli/blob/master/README.md[Angular-CLI README]."
msgstr "Um mehr Hilfe bei ' angular-cli ' zu erhalten, verwenden Sie ' ng help ' oder schauen Sie sich den link:https:/github.com/angularar-cli/blob/master/README.md[Angular-CLI README] an."

#. type: Plain text
#: ui/build.adoc:24
msgid "Be aware that there is currently a link:https://github.com/angular/angular-cli/issues/11208[bug] in how angular-cli generates the service-worker configuration file (ngsw-config.json). After running the above command it is required to fix 'regex' statements in the file, replacing double '`\\\\`' by single '`\\`' to avoid wrong escaping."
msgstr ""
"Seien Sie sich bewusst, dass es derzeit eine link:https://github.com/angular/angular-cli/issues/11208[bug] in der Art und Weise gibt, wie angular-cli die Konfigurationsdatei des Service-Workers (ngsw-config.json) erzeugt. Nach dem Ausführen des obigen Befehls ist es erforderlich, ' regex '-Anweisungen in der Datei zu beheben, indem man die doppelte ' \\ \\ ' ' durch eine einzelne ' \\ ' ' ersetzt, um ein falsches Ausweichen "
"zu vermeiden."

#. type: Title ===
#: ui/build.adoc:25
#, no-wrap
msgid "Build for OpenEMS Backend"
msgstr "**OpenEMS UI** ist die generische Benutzeroberfläche"

#. type: Plain text
#: ui/build.adoc:28 ui/build.adoc:36
msgid "Execute"
msgstr "Ausführen"

#. type: Plain text
#: ui/build.adoc:30
msgid "`ng build -c backend`"
msgstr "' Ng build-c backend '"

#. type: Plain text
#: ui/build.adoc:32
msgid "The build artifacts are created in the directory `ui/target/backend`."
msgstr "Die Build-Artefakte werden im Verzeichnis ' ui/target/backend ' erstellt."

#. type: Title ===
#: ui/build.adoc:33
#, no-wrap
msgid "Build for OpenEMS Edge"
msgstr "**OpenEMS Edge** läuft vor Ort und steuert die lokalen Geräte"

#. type: Plain text
#: ui/build.adoc:38
msgid "`ng build -c edge`"
msgstr "' Ng build-c edge '"

#. type: Plain text
#: ui/build.adoc:39
msgid "The build artifacts are created in the directory `ui/target/edge`."
msgstr "Die Build-Artefakte werden im Verzeichnis ' ui/target/edge ' erstellt."
