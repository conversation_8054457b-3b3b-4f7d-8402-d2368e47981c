= Getting Started
:imagesdir: ../assets/images
:sectnums:
:sectnumlevels: 4
:toclevels: 4
:experimental:
:keywords: AsciiDoc
:source-highlighter: highlight.js
:icons: font

There are three ways to get started with OpenEMS. 
You can either setup your development environment and compile OpenEMS Edge from source, you can use pre-built https://github.com/OpenEMS/openems/releases[Release] packages, or you can use the https://github.com/OpenEMS/openems/tree/main/tools/docker/[Docker] images.
How to use the Docker images is described in the README linked above.
For the other two ways, keep reading.
Either way, once finished you will have a working instance of OpenEMS Edge, with simulated energy storage and photovoltaic system, as well as an OpenEMS UI for monitoring the simulator inside your web browser. 
Finally OpenEMS Backend is added to simulate a cloud backend that connects UI and Edge.
If you chose to setup the development environment, skip the first section and go to <<download-the-source-code>>.

== Running the pre-built OpenEMS Edge

. Go to the https://github.com/OpenEMS/openems/releases/[OpenEMS' release Page] on GitHub and download the latest `openems-edge.jar`.
. Open up your Terminal and go to the directory you downloaded the packages to.
. Start OpenEMS Edge with the following command `java -jar openems-edge.jar`

NOTE: Make sure the right java Version (21.0) is installed and active on your system. 
On most systems `java -version` in your CLI will show the active version.
On how to change the active version, refer to your system documentation.

. If you see the following warning in your terminal: *There are no schedulers configured!* OpenEMS Edge runs perfectly. :-)
. Now you can continue with <<starting-a-simulation>>

== Download the source code [[download-the-source-code]]

. Download any https://git-scm.com[git client icon:external-link[]] and install it. Our recommendation is https://www.sourcetreeapp.com/[Sourcetree by Atlassian icon:external-link[]]

. Clone the OpenEMS git repository

.. In Sourcetree:

... press btn:[File] -> btn:[Clone]
... enter the git repository path `https://github.com/OpenEMS/openems.git`
... select a target directory, for example `C:\Users\<USER>\git\openems`
... and press btn:[Clone].
+
.Cloning the git repository using Sourcetree
image::sourcetree.png[Cloning the git repository using Sourcetree]

.. Alternatively: with the git command line utility

... open a console
... change to the target directory
... execute `git clone https://github.com/OpenEMS/openems.git`

. Git is downloading the complete source code for you.

NOTE: OpenEMS uses the **git** version control system via the popular GitHub platform. It's worth learning some basic git commands and workflows. If you want to start with your own OpenEMS developments, just create a copy using the GitHub https://docs.github.com/en/get-started/quickstart/fork-a-repo[fork a repo] feature, and https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/creating-a-pull-request-from-a-fork[create a pull request] to get feedback on your work and contribute to the project. 

== Setup Eclipse IDE for OpenEMS Edge and Backend

NOTE: Eclipse IDE is the recommended development environment for newcomers to OpenEMS. If you are more familiar with IntelliJ IDEA, feel free to use it. Follow xref:intellij.adoc[this guide].

. Prepare Eclipse IDE
.. Download Java Development Kit (JDK) 21 and install it. We recommend the https://adoptium.net/de/temurin/releases/?version=21[OpenJDK Temurin builds by the Adoptium project]
.. Download https://www.eclipse.org/downloads/[Eclipse for Java icon:external-link[]], install and start it
.. On first start you will get asked to create a workspace.
Select your source code directory (`C:\Users\<USER>\git\openems` in our example) and press btn:[Launch].
+
.Creating a workspace in Eclipse IDE
image::eclipse-workspace.png[Creating a workspace in Eclipse IDE]

.. Install http://bndtools.org[BndTools icon:external-link[]] in Eclipse:
+
Menu: btn:[Help] → btn:[Eclipse Marketplace...] → btn:[Find:] → enter btn:[Bndtools] → press btn:[Install]

.. Configure Eclipse IDE to use JDK 21.
+
- In the Menu select btn:[Windows] → btn:[Preferences]
- Select btn:[Java] - btn:[Installed JREs] in the navigation tree
- Press the btn:[Add...] button
- Keep btn:[Standard VM] selected and press btn:[Next >]
- Press the btn:[Directory...] button and select the folder of the installed JDK (e.g. `C:\Program Files\Eclipse Adoptium\jdk-********-hotspot`)
- Press the btn:[Finish] button
- Back in the Preferences window, tick the newly added JDK 21 and press btn:[Apply and Close]
+
.Creating a workspace in Eclipse IDE
image::eclipse-select-jdk.png[Set the Java Development Kit in Eclipse IDE]

. Import OpenEMS component projects (OSGi bundles):
+
Menu: btn:[File] →  btn:[Import...] → btn:[Bndtools] → btn:[Existing Bnd Workspace] → your source code directory should have been selected as *Root Directory* automatically → btn:[Finish] → "Switch to Bndtools perspective?" btn:[Yes]

. Eclipse will now spend some time importing and building OpenEMS Edge and Backend. Close the *Welcome* window to follow the progress till you eventually see the full tree of OpenEMS projects (or bundles) in the *Bndtools Explorer* and all errors and warnings in the *Problems* tab disappear.

// TODO add Setup of Checkstyle

== Run OpenEMS Edge from Eclipse IDE

. Run OpenEMS Edge
.. In Eclipse IDE open the project btn:[io.openems.edge.application] and double click on btn:[EdgeApp.bndrun].
+
.io.openems.edge.application project in Eclipse IDE
image::eclipse-io.openems.edge.application.png[io.openems.edge.application project in Eclipse IDE]
+
NOTE: Instead of navigating through the projects tree, you can simply use the keyboard shortcut btn:[Ctrl] + btn:[Shift] + btn:[R] to start the "Open Resource" dialog. Enter "EdgeApp.bndrun" there and press btn:[Enter] to open the file.
+
The `EdgeApp.bndrun` file declares all the bundles and runtime properties. For now it should not be necessary to edit it, but it hides some useful settings under the btn:[Source] tab:
+
- `org.osgi.service.http.port=8080`: start the Apache Felix Web Console on port `8080`
- `felix.cm.dir=c:/openems/config`: persist configurations in the folder `c:/openems/config`. Adjust this if you are working on Linux to keep your configurations after restart
- `openems.data.dir=c:/openems/data`: this is where bundles are allowed to persist data. It is used e.g. by the RRD4j timedata storage

.. Click on btn:[Run OSGi] to run OpenEMS Edge. You should see log outputs in the **Console** tab inside Eclipse IDE.
+
.OpenEMS Edge initial log output
image::eclipse-edge-initial-log-output.png[OpenEMS Edge initial log output]

== Starting a simulation [[starting-a-simulation]]

. Open the http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]]
+
Login with username *admin* and password *admin*.
+
.Apache Felix Web Console Configuration
image::apache-felix-console-configuration.png[Apache Felix Web Console Configuration]

. Configure a Scheduler
+
NOTE: The Scheduler is responsible for executing the control algorithms (Controllers) in order and defines the OpenEMS Edge application cycle

.. Click on _**Scheduler All Alphabetically**_
+
.Configuration of All Alphabetically Scheduler
image::config-scheduler-all-alphabetically.png[Configuration of All Alphabetically Scheduler]

.. Accept the default values and click btn:[Save]

.. You created your first instance of an OpenEMS Component with ID "scheduler0". The log shows:
+
```
INFO  [onent.AbstractOpenemsComponent] [scheduler0] Activate Scheduler.AllAlphabetically
```
+
Add any other OpenEMS Components in the same way.
+
NOTE: Once everything is setup you can configure Components more easily via OpenEMS UI using the "Install components" feature in the Settings.

. Configure debug outputs on the console: _**Controller Debug Log**_. The default values can be accepted without changes.
+
.Configuration of Controller Debug Log
image::config-controller-debug-log.png[Configuration of Controller Debug Log]
+
The log shows:
+
```
INFO  [onent.AbstractOpenemsComponent] [ctrlDebugLog0] Activate Controller.Debug.Log
```
+
followed once per second by
+
```
INFO  [ntroller.debuglog.DebugLogImpl] [ctrlDebugLog0] _sum[State:Ok]
```
+
NOTE: It is 'once per second', because the Cycle-Time is defined as "1000 ms" by default. Adjust the setting in the _**Core Cycle (Core.Cycle)**_ component to change this.

. Configure a simulated standard-load-profile datasource using _**Simulator DataSource: CSV Predefined**_. Select `H0_HOUSEHOLD_SUMMER_WEEKDAY_STANDARD_LOAD_PROFILE` as the `Source`.
+
.Configuration of Simulator DataSource: CSV Predefined as standard load profile datasource
image::config-simulator-datasource-standard-load-profile.png[Configuration of Simulator DataSource: CSV Predefined as standard load profile datasource]
+
The log shows:
+
```
INFO  [onent.AbstractOpenemsComponent] [datasource0] Activate Simulator.Datasource.CSV.Predefined
```
+
NOTE: The data source was configured with the OpenEMS Component ID `datasource0` which will be used in the next step as the `Datasource-ID` reference.

. Configure a simulated grid meter: _**Simulator GridMeter Acting**_. Configure the `Datasource-ID 'datasource0'` to refer to the data source configured above.
+
.Configuration of Simulator GridMeter Acting
image::config-simulator-grid-meter-acting.png[Configuration of Simulator GridMeter Acting]
+
This time some more logs will appear. Most importantly they show, that the Grid meter now measures (simulates) a power value and the Consumption is derived directly from this value, because no PV system or energy storage system is configured yet.
+
```
INFO  [onent.AbstractOpenemsComponent] [meter0] Activate Simulator.GridMeter.Acting
INFO  [onent.AbstractOpenemsComponent] [meter0] Deactivate Simulator.GridMeter.Acting
INFO  [onent.AbstractOpenemsComponent] [meter0] Activate Simulator.GridMeter.Acting
INFO  [ntroller.debuglog.DebugLogImpl] [ctrlDebugLog0] _sum[State:Ok Grid:1336 W Consumption:1336 W] meter0[1336 W]
```
+
NOTE: This setup causes the simulated grid-meter to take the standardized load-profiles data as input parameter.
+
NOTE: 'Acting' in the name 'Simulator GridMeter Acting' refers to the fact, that this meter actively provides data - in opposite to a 'Reacting' simulated device that is reacting on other components: for example the 'Simulator.EssSymmetric.Reacting' configured below.

. Configure a simulated reacting energy storage system: _**Simulator EssSymmetric Reacting**_. The default values can be accepted without changes.
+
.Configuration of Simulator EssSymmetric Reacting
image::config-simulator-esssymmetric-reacting.png[Configuration of Simulator EssSymmetric Reacting]
+
The log shows:
+
```
INFO  [onent.AbstractOpenemsComponent] [ess0] Activate Simulator.EssSymmetric.Reacting
INFO  [ntroller.debuglog.DebugLogImpl] [ctrlDebugLog0] _sum[State:Ok Ess SoC:50 % Grid:1560 W Consumption:1560 W] ess0[SoC:50 %|L:UNDEFINED] meter0[1560 W]
INFO  [ntroller.debuglog.DebugLogImpl] [ctrlDebugLog0] _sum[State:Ok Ess SoC:50 %|L:0 W Grid:1502 W Consumption:1502 W] ess0[SoC:50 %|L:0 W] meter0[1502 W]
```
+
NOTE: The debug log now shows data for the battery, but the charge/discharge power stays at "0 W" and the state of charge stays at "50 %" as configured. Next step is to configure a control algorithm that tells the battery to charge or discharge depending on the power measured by the simulated grid meter.

. Configure the self-consumption optimization algorithm: _**Controller Ess Balancing**_. Configure the `Ess-ID` `'ess0'` and `Grid-Meter-ID` `'meter0'` to refer to the components configured above.
+
.Configuration of Controller Ess Balancing
image::config-controller-ess-balancing.png[Configuration of Controller Ess Balancing]
+
The log shows:
+
```
INFO  [onent.AbstractOpenemsComponent] [ctrlBalancing0] Activate Controller.Symmetric.Balancing
...
INFO  [ntroller.debuglog.DebugLogImpl] [ctrlDebugLog0] _sum[State:Ok Ess SoC:50 %|L:593 W Grid:15 W Consumption:608 W] ess0[SoC:49 %|L:593 W|DebugSetActivePower:593 W] meter0[15 W]
```
+
NOTE: Values will differ slightly for you, but note how the Controller now tells the battery to discharge (`Ess SoC:49 %|L:593 W`), trying to balance the Grid power to "0 W" (`Grid L:15 W`):

. Configure the websocket Api Controller: _**Controller Api Websocket**_. The default values can be accepted without changes.
+
.Configuration of Controller Api Websocket
image::config-controller-api-websocket.png[Configuration of Controller Api Websocket]
+
The log shows:
+
```
INFO  [onent.AbstractOpenemsComponent] [ctrlApiWebsocket0] Activate Controller.Api.Websocket
INFO  [socket.AbstractWebsocketServer] Starting [Websocket Api] websocket server [port=8085]
```
+
NOTE: The Controller Api Websocket is required so that OpenEMS UI can connect to OpenEMS Edge locally.

== Run OpenEMS UI

NOTE: If you plan to actively develop on OpenEMS UI, you can now also setup a development environment for it using xref:ui/setup-ide.adoc[this guide]. Otherwise you can use the prebuilt UI or [deploy the OpenEMS UI as docker image](https://openems.github.io/openems.io/openems/latest/edge/deploy/docker.html#_openems_edge_ui).

. Make sure OpenEMS Edge is running locally and the websocket is running on port `8085`.

. Open the UI URL

. You should see OpenEMS UI. Log in as user "guest" by leaving the standard password and clicking the login button. Alternatively type "admin" in the password field to log in with extended permissions.
+
.OpenEMS UI Login screen
image::openems-ui-login.png[OpenEMS UI Login screen]

. You should see the Energymonitor showing the same data as the DebugLog output on the console.
+
.OpenEMS UI Energymonitor screen
image::openems-ui-edge-overview.png[OpenEMS UI Energymonitor screen]

.OpenEMS UI Energymonitor screen
image::openems-ui-edge-overview2.png[OpenEMS UI Energymonitor screen]

== Integrate OpenEMS Backend

Instead of having Edge and UI talk to each other directly, the communication can also be proxied via Backend.

=== Run and configure OpenEMS Backend

. In Eclipse IDE open the project btn:[io.openems.backend.application] and double click on btn:[BackendApp.bndrun].
+
.io.openems.backend.application project in Eclipse IDE
image::eclipse-io.openems.backend.application.png[io.openems.backend.application project in Eclipse IDE]

. Click on btn:[Run OSGi] to run OpenEMS Backend. You should see log outputs on the console inside Eclipse IDE.
+
.OpenEMS Backend initial log output
image::eclipse-backend-initial-log-output.png[OpenEMS Backend initial log output]
+
NOTE: Disable the two icon buttons "Show Console When Standard Out changes" and "Show Console When Standard Error changes" next to the _Console_ tab to avoid constant switching between the output of OpenEMS Edge and OpenEMS Backend.

. Configure the Backend
.. Open the http://localhost:8079/system/console/configMgr[Apache Felix Web Console Configuration icon:external-link[]].
+
NOTE: Apache Felix Web Console for OpenEMS Backend is started on port 8079 by default. This is configured using the `org.osgi.service.http.port` setting in BackendApp.bndrun.
+
Login with username *admin* and password *admin*.

.. Configure Edge.Websocket
+
NOTE: The _**Edge.Websocket**_ service is responsible for the communication between OpenEMS Backend and OpenEMS Edge.
+
In the example we are configuring the `Port '8081'`. This port needs to match with what we configure later in OpenEMS Edge. The `Debug Mode 'DETAILED'` setting helps us to get some more details on the internal behaviour. 
+
.Configuration of Backend Edge.Websocket
image::config-backend-edge.websocket.png[Configuration of Backend Edge.Websocket]

.. Configure Ui.Websocket
+
NOTE: The _**Ui.Websocket**_ service is responsible for the communication between OpenEMS Backend and OpenEMS UI.
+
In the example we are configuring the `Port '8082'`. This port needs to match with what we configure later in the OpenEMS UI environment file. We are again setting `Debug Mode 'DETAILED'`
+
.Configuration of Backend Ui.Websocket
image::config-backend-ui.websocket.png[Configuration of Backend Ui.Websocket]

.. Configure Timedata
+
NOTE: The *Timedata* service provider is responsible for holding the current and historic data of each connected Edge device.
+
In the example we are configuring the _**Timedata.Dummy**_ service. The default value for _Component-ID` can be accepted without changes, so just press btn:[Save]. In a production system you would want to use a real implementation like *Timedata.InfluxDB*.
+
.Configuration of Backend Timedata.Dummy
image::config-backend-timedata.dummy.png[Configuration of Backend Timedata.Dummy]

.. Configure Metadata
+
NOTE: The *Metadata* service provider is responsible for authentication of Edge devices and Users connecting via UI.
+
.Configuration of Backend Metadata.Dummy
image::config-backend-metadata.dummy.png[Configuration of Backend Metadata.Dummy]
+
NOTE: In the example we are configuring the _**Metadata.Dummy**_ service. It takes no configuration parameters, so just press btn:[Save]. In a production system you would want to use a real implementation like _**Metadata.File**_, which uses a static JSON file as input, or _**Metadata.Odoo**_, which uses the *Odoo* business software for authentication and IoT device management. This will require the https://github.com/OpenEMS/odoo-openems[Odoo-OpenEMS-Addon] to be installed on your Odoo instance. See the https://gitpod.io/#https://github.com/OpenEMS/openems/tree/main[OpenEMS Live-Demo Gitpod workspace] for a full, production ready example configuration. For more information see → xref:simulation/gitpod.adoc[Gitpod Workspace]

.. Backend is ready
+
You should have seen some important log messages by now, that indicate that the OpenEMS Backend is ready to accept connections:
```
INFO  [d.timedata.dummy.TimedataDummy] [Timedata.Dummy] Activate
INFO  [d.metadata.dummy.MetadataDummy] [Metadata.Dummy] Activate
INFO  [socket.AbstractWebsocketServer] [Ui.Websocket] Starting websocket server [port=8082]
INFO  [socket.AbstractWebsocketServer] [Edge.Websocket] Starting websocket server [port=8081]
```

=== Configure OpenEMS Edge

Next we will configure OpenEMS Edge to connect to the OpenEMS Backend _**Edge.Websocket**_ service. 

. Switch back to the http://localhost:8080/system/console/configMgr[Apache Felix Web Console Configuration for OpenEMS Edge icon:external-link[]].

. Configure the _**Controller Api Backend**_ Component. The default values can be accepted without changes right now.
+
.Configuration of Controller Api Backend
image::config-controller-api-backend.png[Configuration of Controller Api Backend]
+
Some configuration parameters are still noteworthy here:
+
.. _Apikey_ is used to authenticate this Edge at the Backend Metadata service. It has to be unique for each Edge.
.. _Uri_ is set to `ws://localhost:8081`. This defines an unencrypted websocket (`ws://`) connection to the local computer on port `8081` like we configured before for the _**Edge.Websocket**_. For a production setup you would want to use a TLS encrypted websocket with a `wss://` uri.
+
Once you press btn:[save] you should see logs in OpenEMS Edge
+
```
INFO  [onent.AbstractOpenemsComponent] [ctrlBackend0] Activate Controller.Api.Backend
INFO  [socket.AbstractWebsocketClient] [ctrlBackend0] Opening connection to websocket server [ws://localhost:8081]
INFO  [socket.ClientReconnectorWorker] [ctrlBackend0] Connecting WebSocket... [NOT_YET_CONNECTED]
INFO  [socket.ClientReconnectorWorker] [ctrlBackend0] Connected WebSocket successfully [0s]
INFO  [.controller.api.backend.OnOpen] [ctrlBackend0] Connected to OpenEMS Backend
```
+
and OpenEMS Backend
+
```
INFO  [s.backend.common.metadata.Edge] Edge [edge0]: Update version from [0.0.0] to [...]
INFO  [mon.metadata.SimpleEdgeHandler] Edge [edge0]. Update config: ...
INFO  [dgewebsocket.EdgeWebsocketImpl] [monitor] Edge-Connections: 1
```

=== Connect OpenEMS UI with Backend

_(You need to have completed the xref:ui/setup-ide.adoc[OpenEMS UI guide] for the following steps)_

. In the Visual Studio Code terminal stop the running `ng serve...` by pressing btn:[ctrl] + btn:[c]

. Restart OpenEMS UI in 'local backend mode':
+
`ng serve -c openems-backend-dev`

NOTE: OpenEMS UI can work both for local connections to OpenEMS Edge as well as cloud connections to OpenEMS Backend. The switch requires some basic parameters that are defined in 'environment' files `ui/src/themes/openems/environments`. The possible parameters for `ng serve -c...` are defined in the `ui/angular.json` file. 

. Open a browser at http://localhost:4200

. You should see OpenEMS UI Login. Log in with any email / username and password.
+
NOTE: _**Metadata.Dummy**_ accepts any user/password combination. For production use, switch to a different *Metadata* implementation as described above.
+
.OpenEMS UI Login screen
image::openems-ui-backend-login.png[OpenEMS UI Login screen]

. You will be presented an overview list of all connected OpenEMS Edge devices you have permissions for:
+
.OpenEMS UI Overview screen
image::openems-ui-backend-overview.png[OpenEMS UI Overview screen]

. Click on *OpenEMS Edge #0* to see the same live-view as before on the local connection.
+
.OpenEMS UI Live screen
image::openems-ui-backend-live.png[OpenEMS UI Live screen]

## Next steps

Now that you setup a complete development environment and have a working instance of OpenEMS Edge, OpenEMS Backend an OpenEMS UI, you can continue implementing your first device driver in OpenEMS. We provide a tutorial that explains the steps to implement an electric meter in OpenEMS Edge that is connected via Modbus/TCP.

The meter itself is simulated using a small Modbus slave application, so no external hardware is required for this guide. → xref:edge/implement.adoc[Implementing a Device]

## Help

If you experienced any problems or doubts, please get in touch with us on the https://community.openems.io/[OpenEMS Community] forum.
