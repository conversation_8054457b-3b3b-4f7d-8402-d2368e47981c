= Build OpenEMS UI
:sectnums:
:sectnumlevels: 4
:toclevels: 4
:experimental:
:keywords: AsciiDoc
:source-highlighter: highlight.js
:icons: font
:imagesdir: ../../assets/images

This chapter explains, how OpenEMS UI can be compiled so that it can be executed outside of an IDE.

Open the terminal inside Visual Studio Code (Menu: btn:[Terminal] -> btn:[New Terminal]).

== Build using angular-cli from command line

Execute the following commands inside the `ui` directory of your OpenEMS repository. The build artifacts will be stored in a subfolder of the `target` directory

To get more help on `angular-cli` use `ng help` or go check out the link:https://github.com/angular/angular-cli/blob/master/README.md[Angular-CLI README].

=== Preparation

- Install all dependencies
+
`npm update`

=== OpenEMS Edge

NOTE: This expects an OpenEMS Edge with *Controller.Api.Websocket* on default port `8075`

- Serve to port `4200`
+
`ng serve -o -c openems-edge-dev`

- Build Development
+
`ng build -c "openems,openems-edge-dev"`

- Build Production
+
`ng build -c "openems,openems-edge-prod,prod"`

=== OpenEMS Backend

NOTE: This expects an OpenEMS Backend with *Ui.Websocket* on default port `8082`

- Serve to port `4200`
+
`ng serve -o -c openems-backend-dev`

- Build Development
+
`ng build -c "openems,openems-backend-dev"`

- Build Production
+
`ng build -c "openems,openems-backend-prod,prod"`

== Creating a Theme

OpenEMS UI is able to support different themes, e.g. if you need to provide different colour sets for different OEM customers.

To create a new Theme for OpenEMS UI:

- Create new folder under `ui/src/themes`

- Files in `root` will be copied to `/` of the OpenEMS UI

- `scss/variables.scss` will be used for styling

- `environments/*.ts` define settings for Backend/Edge and development/production environments

- Generate contents of `root` folder using https://realfavicongenerator.net. and place them in `root` subdirectory

- Add entries in `angular.json`
