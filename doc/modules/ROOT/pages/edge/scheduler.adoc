= Scheduler
:sectnums:
:sectnumlevels: 4
:toclevels: 4
:experimental:
:keywords: AsciiDoc
:source-highlighter: highlight.js
:icons: font
:imagesdir: ../../assets/images

A OpenEMS Edge Scheduler plans the execution of Controllers. It defines...

- which Controllers are executed
- in which priority the Controllers are executed

include::scheduler.adoc.d/_include.adoc[leveloffset=+0]

// TODO
//== Developing a Scheduler