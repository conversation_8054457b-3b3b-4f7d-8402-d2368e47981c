= Real-Time Simulation
:imagesdir: ../../assets/images
:sectnumlevels: 0
:toclevels: 0

OpenEMS provides several virtual devices for testing and development. To set up the simulation-environment follow the setup guide in xref:gettingstarted.adoc[Getting Started]. The Apache Felix Web Console Configuration lists all OpenEMS component, but those which rely on external hardware can't be used for simulation. These are replaced by more generic components tagged with the "Simulator" prefix. Still, not all non-simulator devices rely on real hardware. Other components used in production, like Loggers, Controllers and Schedulers can be combined with the virtual devices to build a "living" system.

== Types of Simulated Components

* **DataSource**: A data-source generates lists of data and provides them as a channel to other components. The data can be generated by different means, which could be an augmented random-generator or a reader for a specific data-format like the "Simulator Datasource: CSV Reader".
* **Devices**: Devices replace a hardware-component like a meter or an ess. There are two kinds of devices:
** **acting**: These devices act on their own. Their actions do not depend on other device's actions, but on data-sources.
** **reacting**: These devices analyse other device's channels/properties to deduce their own behavior.

== Simulator Components

* `Simulator DataSource: CSV Reader`: Reads .csv files and provides their content according to the `io.openems.edge.simulator.datasource.api.SimulatorDatasource` interface. Its package contains several sample csv-files for different devices and situations.
* `Simulator EssSymmetric Reacting`: Simulates the State of Charge of a ess device. Its behavior depends on configured controllers.
* `Simulator GridMeter Acting`: Intended to represent grid consumption/production. Portrays a datasource' values. It requests those tagged with the "ActivePower" key. The values are portrayed in the `SimulatedActivePower`, `ActivePower` and - divided by three - in the `ActivePowerL1`, `ActivePowerL2` and `ActivePowerL3` channels.
* `Simulator ProductionMeter Acting`: Intended to represent fixed production like photovoltaic arrays. To date the functionality equals the one of `Simulator GridMeter Acting`.
* `Simulator NRCMeter Acting`: Intended to represent non-regulated-consumption. To date the functionality equals the one of `Simulator GridMeter Acting`.
* `Simulator GridMeter Reacting`: Intended to represent grid consumption/production. Takes all meters (but grid-meters) and esss, sums up their ActivePowers and divides this value by the number of grid-meters in the system. The negative of this result is then portrayed in the `SimulatedActivePower`, `ActivePower` and - divided by three - in the `ActivePowerL1`, `ActivePowerL2` and `ActivePowerL3` channels. This equals the physical reaction of the real grid.

== Possible Combinations

In reality the OpenEMS can not measure the non-regulated-consumers, as they are spread all over the building. However, it can measure the power at the mains connection, which can be used to calculate the non-regulated-consumption. This approach can be simulated using the `Simulator GridMeter Acting` combined with a `Simulator EssSymmetric Reacting`.

For intelligent systems going beyond real-time-regulation, however, having a - even in production - virtual meter measuring the non-regulated-consumers can be a major advantage. In order to test algorithms depending on such an environment, the simulator provides the `Simulator ProductionMeter Acting`, the `Simulator NRCMeter Acting` and the `Simulator GridMeter Reacting`.