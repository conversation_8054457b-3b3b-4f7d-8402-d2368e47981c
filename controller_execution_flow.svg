<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .box { fill: #e1f5fe; stroke: #0277bd; stroke-width: 2; }
      .process { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .decision { fill: #fff3e0; stroke: #ef6c00; stroke-width: 2; }
      .controller { fill: #e8f5e8; stroke: #2e7d32; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="400" y="30" class="title">OpenEMS Controller 加载和执行流程</text>
  
  <!-- OSGi Container Start -->
  <rect x="300" y="60" width="200" height="40" class="box" rx="5"/>
  <text x="400" y="85" class="text">OSGi 容器启动</text>
  
  <!-- Component Registration -->
  <rect x="250" y="130" width="300" height="60" class="process" rx="5"/>
  <text x="400" y="150" class="text">组件注册和依赖注入</text>
  <text x="400" y="165" class="small-text">@Component, @Reference</text>
  <text x="400" y="180" class="small-text">ComponentManager, ESS, Meter</text>
  
  <!-- Controller Activation -->
  <rect x="100" y="220" width="180" height="60" class="controller" rx="5"/>
  <text x="190" y="240" class="text">PeakShaving Controller</text>
  <text x="190" y="255" class="small-text">@Activate 方法</text>
  <text x="190" y="270" class="small-text">配置解析和初始化</text>
  
  <rect x="520" y="220" width="180" height="60" class="controller" rx="5"/>
  <text x="610" y="240" class="text">BalancingSchedule Controller</text>
  <text x="610" y="255" class="small-text">@Activate 方法</text>
  <text x="610" y="270" class="small-text">配置解析和初始化</text>
  
  <!-- Scheduler Configuration -->
  <rect x="250" y="310" width="300" height="60" class="process" rx="5"/>
  <text x="400" y="330" class="text">调度器配置</text>
  <text x="400" y="345" class="small-text">ProductionSchedulerOrderDefinition</text>
  <text x="400" y="360" class="small-text">PeakShaving → BalancingSchedule</text>
  
  <!-- Cycle Worker Start -->
  <rect x="300" y="400" width="200" height="40" class="box" rx="5"/>
  <text x="400" y="425" class="text">CycleWorker 启动</text>
  
  <!-- Main Execution Loop -->
  <rect x="250" y="470" width="300" height="40" class="process" rx="5"/>
  <text x="400" y="495" class="text">主执行循环 (默认1000ms)</text>
  
  <!-- Pre-execution Events -->
  <rect x="250" y="540" width="300" height="40" class="process" rx="5"/>
  <text x="400" y="565" class="text">执行前事件和组件更新</text>
  
  <!-- Controller Execution Decision -->
  <polygon points="400,600 480,630 400,660 320,630" class="decision"/>
  <text x="400" y="635" class="text">控制器可用且启用?</text>
  
  <!-- PeakShaving Execution -->
  <rect x="50" y="700" width="200" height="80" class="controller" rx="5"/>
  <text x="150" y="720" class="text">PeakShaving.run()</text>
  <text x="150" y="735" class="small-text">• 检查时间段配置</text>
  <text x="150" y="750" class="small-text">• 状态机逻辑</text>
  <text x="150" y="765" class="small-text">• 计算削峰功率</text>
  <text x="150" y="780" class="small-text">• 设置ESS功率</text>
  
  <!-- BalancingSchedule Execution -->
  <rect x="550" y="700" width="200" height="80" class="controller" rx="5"/>
  <text x="650" y="720" class="text">BalancingSchedule.run()</text>
  <text x="650" y="735" class="small-text">• 获取调度设定点</text>
  <text x="650" y="750" class="small-text">• 检查ESS网格模式</text>
  <text x="650" y="765" class="small-text">• 计算充放电功率</text>
  <text x="650" y="780" class="small-text">• 设置ESS功率</text>
  
  <!-- Power Coordination -->
  <rect x="250" y="810" width="300" height="60" class="process" rx="5"/>
  <text x="400" y="830" class="text">功率协调和约束优化</text>
  <text x="400" y="845" class="small-text">高优先级控制器设置约束</text>
  <text x="400" y="860" class="small-text">低优先级控制器在约束内操作</text>
  
  <!-- Error Handling -->
  <rect x="300" y="900" width="200" height="40" class="decision" rx="5"/>
  <text x="400" y="925" class="text">错误处理和状态监控</text>
  
  <!-- Back to Loop -->
  <rect x="300" y="960" width="200" height="20" class="small-text" rx="5" fill="none" stroke="none"/>
  <text x="400" y="975" class="small-text">返回主循环</text>
  
  <!-- Arrows -->
  <line x1="400" y1="100" x2="400" y2="130" class="arrow"/>
  <line x1="400" y1="190" x2="190" y2="220" class="arrow"/>
  <line x1="400" y1="190" x2="610" y2="220" class="arrow"/>
  <line x1="190" y1="280" x2="400" y2="310" class="arrow"/>
  <line x1="610" y1="280" x2="400" y2="310" class="arrow"/>
  <line x1="400" y1="370" x2="400" y2="400" class="arrow"/>
  <line x1="400" y1="440" x2="400" y2="470" class="arrow"/>
  <line x1="400" y1="510" x2="400" y2="540" class="arrow"/>
  <line x1="400" y1="580" x2="400" y2="600" class="arrow"/>
  <line x1="320" y1="630" x2="150" y2="700" class="arrow"/>
  <line x1="480" y1="630" x2="650" y2="700" class="arrow"/>
  <line x1="150" y1="780" x2="400" y2="810" class="arrow"/>
  <line x1="650" y1="780" x2="400" y2="810" class="arrow"/>
  <line x1="400" y1="870" x2="400" y2="900" class="arrow"/>
  <line x1="400" y1="940" x2="400" y2="960" class="arrow"/>
  
  <!-- Loop back arrow -->
  <path d="M 500 970 Q 600 970 600 500 Q 600 480 580 480" class="arrow" fill="none"/>
  
  <!-- Yes/No labels -->
  <text x="280" y="650" class="small-text">是</text>
  <text x="520" y="650" class="small-text">是</text>
  
</svg>