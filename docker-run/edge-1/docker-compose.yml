services:
  openems-edge:
    image: openems/edge:latest
    container_name: openems_edge
    hostname: openems_edge_1
    restart: unless-stopped
    volumes:
      - openems-edge-conf-1:/var/opt/openems/config:rw
      - openems-edge-data-1:/var/opt/openems/data:rw
    ports:
      - 8090:8080 # Apache-Felix
      - 8095:8085 # UI-Websocket

  openems-ui:
    image: openems/ui-edge:latest
    container_name: openems_ui
    hostname: openems_ui_1
    restart: unless-stopped
    volumes:
      - openems-ui-conf-1:/etc/nginx:rw
      - openems-ui-log-1:/var/log/nginx:rw
    environment:
      - UI_WEBSOCKET=ws://localhost:8095 # Connect to openems_edge container
    ports:
      - 8089:80
      - 8443:443

volumes:
  openems-edge-conf-1:
  openems-edge-data-1:
  openems-ui-conf-1:
  openems-ui-log-1:
