services:
  openems_backend:
    image: openems/backend:latest
    container_name: openems_backend
    hostname: openems_backend
    restart: unless-stopped
    volumes:
      - openems-backend-conf:/var/opt/openems/config:rw
      - openems-backend-data:/var/opt/openems/data:rw
    ports:
      - 8079:8079 # Apache-Felix
      - 8081:8081 # Edge-Websocket
      - 8082:8082 # UI-Websocket

  openems-ui:
    image: openems/ui-backend:latest
    container_name: openems_ui_backend
    hostname: openems_ui_backend
    restart: unless-stopped
    volumes:
      - openems-ui-backend-conf:/etc/nginx:rw
      - openems-ui-backend-log:/var/log/nginx:rw
    environment:
      - UI_WEBSOCKET=ws://openems_backend:8082 # Backend WebSocket service
    ports:
      - 8089:80
      # - 443:443

  openems_influxdb:
    image: influxdb:1.8.10
    container_name: openems_influxdb
    hostname: openems_influxdb
    restart: unless-stopped
    environment:
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=admin
    volumes:
      - openems-influxdb:/var/lib/influxdb:rw
    ports:
      - 8086:8086

volumes:
  openems-backend-conf:
  openems-backend-data:
  openems-ui-backend-conf:
  openems-ui-backend-log:
  openems-influxdb:
