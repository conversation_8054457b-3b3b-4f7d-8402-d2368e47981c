<svg width="1400" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="dataArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1400" height="1600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">OpenEMS Controller Execution Flow with Data Flow</text>
  
  <!-- Data Sources Section -->
  <rect x="50" y="60" width="300" height="200" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="200" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1976d2">Data Sources</text>
  
  <!-- ESS Data -->
  <rect x="70" y="100" width="120" height="40" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="130" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">ESS Status</text>
  <text x="130" y="128" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">SoC, Power, Limits</text>
  
  <!-- Grid Data -->
  <rect x="210" y="100" width="120" height="40" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="270" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Grid/Meter Data</text>
  <text x="270" y="128" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">P, Q, Frequency</text>
  
  <!-- Config Data -->
  <rect x="70" y="160" width="120" height="40" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="130" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Configuration</text>
  <text x="130" y="188" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Thresholds, Priorities</text>
  
  <!-- Schedule Data -->
  <rect x="210" y="160" width="120" height="40" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="270" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Schedule Data</text>
  <text x="270" y="188" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Time Slots, Targets</text>
  
  <!-- OSGi Container -->
  <rect x="400" y="80" width="200" height="60" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
  <text x="500" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">1. OSGi Container Startup</text>
  <text x="500" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Component Registration</text>
  
  <!-- Controller Activation -->
  <rect x="400" y="170" width="200" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
  <text x="500" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">2. Controller Activation</text>
  <text x="500" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">@Activate Methods</text>
  <text x="500" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• PeakShaving Controller</text>
  <text x="500" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• BalancingSchedule Controller</text>
  
  <!-- Scheduler Configuration -->
  <rect x="400" y="280" width="200" height="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
  <text x="500" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">3. Scheduler Configuration</text>
  <text x="500" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Priority: PeakShaving > BalancingSchedule</text>
  
  <!-- CycleWorker -->
  <rect x="400" y="370" width="200" height="60" fill="#e1f5fe" stroke="#0288d1" stroke-width="2" rx="5"/>
  <text x="500" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">4. CycleWorker Initiation</text>
  <text x="500" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Main Execution Loop</text>
  
  <!-- Controller Execution -->
  <rect x="100" y="480" width="600" height="120" fill="#fff8e1" stroke="#ffa000" stroke-width="2" rx="5"/>
  <text x="400" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">5. Controller Execution</text>
  
  <!-- PeakShaving Controller -->
  <rect x="120" y="520" width="260" height="70" fill="#ffecb3" stroke="#ff8f00" stroke-width="1" rx="3"/>
  <text x="250" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#333">PeakShaving Controller</text>
  <text x="250" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Input: Grid Power, Peak Threshold</text>
  <text x="250" y="568" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Logic: Calculate required ESS power</text>
  <text x="250" y="581" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Output: Power Constraints</text>
  
  <!-- BalancingSchedule Controller -->
  <rect x="420" y="520" width="260" height="70" fill="#ffecb3" stroke="#ff8f00" stroke-width="1" rx="3"/>
  <text x="550" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#333">BalancingSchedule Controller</text>
  <text x="550" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Input: Schedule, SoC, Time</text>
  <text x="550" y="568" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Logic: Time-based power planning</text>
  <text x="550" y="581" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">Output: Power Constraints</text>
  
  <!-- Power Coordination Section -->
  <rect x="50" y="640" width="700" height="300" fill="#e8f5e8" stroke="#4caf50" stroke-width="3" rx="5"/>
  <text x="400" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2e7d32">6. Power Coordination & Optimization</text>
  
  <!-- EssPowerImpl -->
  <rect x="80" y="680" width="180" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="3"/>
  <text x="170" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#333">EssPowerImpl</text>
  <text x="170" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Collect Constraints</text>
  <text x="170" y="728" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Validate Solvability</text>
  <text x="170" y="741" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Manage ESS Components</text>
  <text x="170" y="754" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Event Handling</text>
  
  <!-- Constraint Data -->
  <rect x="300" y="680" width="160" height="60" fill="#e1f5fe" stroke="#0288d1" stroke-width="1" rx="3"/>
  <text x="380" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#333">Constraint Data</text>
  <text x="380" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Power Limits</text>
  <text x="380" y="728" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Relationship (=, ≤, ≥)</text>
  
  <!-- Solver -->
  <rect x="500" y="680" width="220" height="100" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="3"/>
  <text x="610" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#333">Solver</text>
  <text x="610" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Linear Programming</text>
  <text x="610" y="728" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Strategy Selection</text>
  <text x="610" y="741" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• OPTIMIZE_BY_KEEPING_ALL_EQUAL</text>
  <text x="610" y="754" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• OPTIMIZE_BY_MOVING_TOWARDS_TARGET</text>
  <text x="610" y="767" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Exception Handling</text>
  
  <!-- Optimization Data -->
  <rect x="80" y="790" width="180" height="80" fill="#e1f5fe" stroke="#0288d1" stroke-width="1" rx="3"/>
  <text x="170" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#333">Optimization Data</text>
  <text x="170" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Coefficients Matrix</text>
  <text x="170" y="838" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• ESS Capabilities</text>
  <text x="170" y="851" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Target Direction</text>
  <text x="170" y="864" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Solution Map</text>
  
  <!-- Solution Data -->
  <rect x="300" y="790" width="160" height="60" fill="#e1f5fe" stroke="#0288d1" stroke-width="1" rx="3"/>
  <text x="380" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#333">Solution Data</text>
  <text x="380" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Inverter Power Map</text>
  <text x="380" y="838" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Active/Reactive Power</text>
  
  <!-- Apply Solution -->
  <rect x="500" y="810" width="220" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="3"/>
  <text x="610" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#333">Apply Solution</text>
  <text x="610" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• applySolution() Method</text>
  <text x="610" y="858" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Set ESS Power Values</text>
  
  <!-- ESS Components -->
  <rect x="800" y="680" width="200" height="100" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
  <text x="900" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">ESS Components</text>
  <text x="900" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">ManagedSymmetricEss</text>
  <text x="900" y="735" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">ManagedAsymmetricEss</text>
  <text x="900" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• applyPower() Method</text>
  <text x="900" y="763" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">• Power Precision Handling</text>
  
  <!-- Error Handling -->
  <rect x="400" y="980" width="200" height="60" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="5"/>
  <text x="500" y="1005" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">7. Error Handling</text>
  <text x="500" y="1020" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Exception Management</text>
  
  <!-- Loop Back -->
  <rect x="400" y="1070" width="200" height="60" fill="#e8eaf6" stroke="#3f51b5" stroke-width="2" rx="5"/>
  <text x="500" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">8. Loop Back</text>
  <text x="500" y="1110" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Next Cycle Iteration</text>
  
  <!-- Data Flow Arrows -->
  <!-- From Data Sources to Controllers -->
  <line x1="190" y1="140" x2="250" y2="520" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <text x="200" y="330" font-family="Arial, sans-serif" font-size="9" fill="#0066cc">ESS Data</text>
  
  <line x1="270" y1="140" x2="250" y2="520" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <text x="280" y="330" font-family="Arial, sans-serif" font-size="9" fill="#0066cc">Grid Data</text>
  
  <line x1="270" y1="180" x2="550" y2="520" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <text x="400" y="350" font-family="Arial, sans-serif" font-size="9" fill="#0066cc">Schedule</text>
  
  <line x1="130" y1="180" x2="550" y2="520" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <text x="320" y="350" font-family="Arial, sans-serif" font-size="9" fill="#0066cc">Config</text>
  
  <!-- From Controllers to Power Coordination -->
  <line x1="250" y1="590" x2="170" y2="680" stroke="#0066cc" stroke-width="3" marker-end="url(#dataArrow)"/>
  <text x="180" y="635" font-family="Arial, sans-serif" font-size="10" fill="#0066cc" font-weight="bold">Constraints</text>
  
  <line x1="550" y1="590" x2="170" y2="680" stroke="#0066cc" stroke-width="3" marker-end="url(#dataArrow)"/>
  
  <!-- Within Power Coordination -->
  <line x1="260" y1="720" x2="300" y2="720" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <line x1="460" y1="710" x2="500" y2="710" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <line x1="170" y1="790" x2="170" y2="770" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <line x1="260" y1="830" x2="300" y2="830" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <line x1="460" y1="830" x2="500" y2="830" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  
  <!-- To ESS Components -->
  <line x1="720" y1="730" x2="800" y2="730" stroke="#0066cc" stroke-width="3" marker-end="url(#dataArrow)"/>
  <text x="750" y="725" font-family="Arial, sans-serif" font-size="10" fill="#0066cc" font-weight="bold">Power Values</text>
  
  <!-- Control Flow Arrows -->
  <line x1="500" y1="140" x2="500" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="250" x2="500" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="340" x2="500" y2="370" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="430" x2="400" y2="480" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="600" x2="400" y2="640" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="940" x2="500" y2="980" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="1040" x2="500" y2="1070" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Loop back arrow -->
  <path d="M 400 1100 Q 200 1100 200 400 Q 200 300 400 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="150" y="750" font-family="Arial, sans-serif" font-size="10" fill="#333" transform="rotate(-90 150 750)">Next Cycle</text>
  
  <!-- Legend -->
  <rect x="1050" y="100" width="300" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="5"/>
  <text x="1200" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">Legend</text>
  
  <line x1="1070" y1="145" x2="1100" y2="145" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="1110" y="150" font-family="Arial, sans-serif" font-size="10" fill="#333">Control Flow</text>
  
  <line x1="1070" y1="165" x2="1100" y2="165" stroke="#0066cc" stroke-width="2" marker-end="url(#dataArrow)"/>
  <text x="1110" y="170" font-family="Arial, sans-serif" font-size="10" fill="#0066cc">Data Flow</text>
  
  <rect x="1070" y="180" width="20" height="15" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
  <text x="1100" y="192" font-family="Arial, sans-serif" font-size="10" fill="#333">Data Sources</text>
  
  <rect x="1070" y="200" width="20" height="15" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
  <text x="1100" y="212" font-family="Arial, sans-serif" font-size="10" fill="#333">Processing Components</text>
  
  <rect x="1070" y="220" width="20" height="15" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
  <text x="1100" y="232" font-family="Arial, sans-serif" font-size="10" fill="#333">System Components</text>
  
  <!-- Data Flow Labels -->
  <text x="1200" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">Key Data Types</text>
  <text x="1070" y="320" font-family="Arial, sans-serif" font-size="9" fill="#333">• ESS Status: SoC, Power, Limits</text>
  <text x="1070" y="335" font-family="Arial, sans-serif" font-size="9" fill="#333">• Grid Data: Active/Reactive Power</text>
  <text x="1070" y="350" font-family="Arial, sans-serif" font-size="9" fill="#333">• Constraints: Power Limits, Relationships</text>
  <text x="1070" y="365" font-family="Arial, sans-serif" font-size="9" fill="#333">• Solutions: Optimized Power Values</text>
  <text x="1070" y="380" font-family="Arial, sans-serif" font-size="9" fill="#333">• Coefficients: Linear Programming Matrix</text>
  
</svg>