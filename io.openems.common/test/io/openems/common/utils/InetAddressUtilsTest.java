package io.openems.common.utils;

import static org.junit.Assert.assertEquals;

import java.net.Inet4Address;
import java.net.UnknownHostException;

import org.junit.Test;

import io.openems.common.exceptions.OpenemsException;

public class InetAddressUtilsTest {

	private static final Inet4Address IP;

	static {
		Inet4Address ip = null;
		try {
			ip = (Inet4Address) Inet4Address.getByName("***********");
		} catch (UnknownHostException uhe) {
			// Handle exception.
		}
		IP = ip;
	}

	@Test
	public void testParse() throws UnknownHostException {
		assertEquals(null, InetAddressUtils.parseOrNull(null));
		assertEquals(null, InetAddressUtils.parseOrNull(""));
		assertEquals(IP, InetAddressUtils.parseOrNull("***********"));
	}

	@Test(expected = OpenemsException.class)
	public void testParseOrError1() throws OpenemsException {
		InetAddressUtils.parseOrError(null);
	}

	@Test(expected = OpenemsException.class)
	public void testParseOrError2() throws OpenemsException {
		InetAddressUtils.parseOrError("");
	}

	@Test
	public void testParseOrError4() throws OpenemsException {
		assertEquals(IP, InetAddressUtils.parseOrError("***********"));
	}

}
